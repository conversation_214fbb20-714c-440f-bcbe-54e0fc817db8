import streamlit as st
import requests
import json

# Set the API base URL
API_BASE_URL = "http://localhost:8000/api/v1"

st.set_page_config(
    page_title="DODO BOOKER Authentication Testing",
    page_icon="🔐",
    layout="wide"
)

st.title("DODO BOOKER Authentication Testing")

# Initialize session state for tokens
if "access_token" not in st.session_state:
    st.session_state.access_token = ""
if "refresh_token" not in st.session_state:
    st.session_state.refresh_token = ""
if "user_id" not in st.session_state:
    st.session_state.user_id = ""

# Display token status in sidebar
st.sidebar.title("Auth Status")
token_status = "✅ Authenticated" if st.session_state.access_token else "❌ Not Authenticated"
st.sidebar.write(f"Status: {token_status}")

if st.session_state.access_token:
    if st.sidebar.button("Logout"):
        st.session_state.access_token = ""
        st.session_state.refresh_token = ""
        st.session_state.user_id = ""
        st.experimental_rerun()

# Authentication tabs
tabs = st.tabs(["Customer Login", "Vendor Login", "Admin Login"])

# Customer Login Tab
with tabs[0]:
    st.header("Customer Authentication")
    
    col1, col2 = st.columns(2)
    
    # OTP Generation
    with col1:
        with st.form("customer_otp_generation"):
            st.subheader("Step 1: Generate OTP")
            phone_number = st.text_input("Phone Number")
            generate_otp = st.form_submit_button("Generate OTP")
            
            if generate_otp and phone_number:
                with st.spinner("Generating OTP..."):
                    try:
                        response = requests.post(
                            f"{API_BASE_URL}/users/auth/customer/",
                            json={"action": "generate_otp", "phone_number": phone_number}
                        )
                        result = response.json()
                        st.write("Response:")
                        st.json(result)
                        
                        # For testing, extract OTP if available
                        if "otp" in result and result["otp"]:
                            st.success(f"OTP for testing: {result['otp']}")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")
    
    # OTP Verification
    with col2:
        with st.form("customer_otp_verification"):
            st.subheader("Step 2: Verify OTP")
            verify_phone = st.text_input("Phone Number", key="verify_phone")
            otp = st.text_input("OTP")
            verify_otp = st.form_submit_button("Verify OTP")
            
            if verify_otp and verify_phone and otp:
                with st.spinner("Verifying OTP..."):
                    try:
                        response = requests.post(
                            f"{API_BASE_URL}/users/auth/customer/",
                            json={"action": "verify_otp", "phone_number": verify_phone, "otp": otp}
                        )
                        result = response.json()
                        
                        # Save tokens if successful
                        if "tokens" in result:
                            st.session_state.access_token = result["tokens"]["access"]
                            st.session_state.refresh_token = result["tokens"]["refresh"]
                            if "user" in result and "id" in result["user"]:
                                st.session_state.user_id = result["user"]["id"]
                            st.success("Authentication successful! Tokens saved.")
                        
                        st.write("Response:")
                        st.json(result)
                    except Exception as e:
                        st.error(f"Error: {str(e)}")

# Vendor Login Tab
with tabs[1]:
    st.header("Vendor Authentication")
    
    col1, col2 = st.columns(2)
    
    # OTP Generation
    with col1:
        with st.form("vendor_otp_generation"):
            st.subheader("Step 1: Generate OTP")
            phone_number = st.text_input("Phone Number", key="vendor_phone")
            generate_otp = st.form_submit_button("Generate OTP")
            
            if generate_otp and phone_number:
                with st.spinner("Generating OTP..."):
                    try:
                        response = requests.post(
                            f"{API_BASE_URL}/users/auth/vendor/",
                            json={"action": "generate_otp", "phone_number": phone_number}
                        )
                        result = response.json()
                        st.write("Response:")
                        st.json(result)
                        
                        # For testing, extract OTP if available
                        if "otp" in result and result["otp"]:
                            st.success(f"OTP for testing: {result['otp']}")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")
    
    # OTP Verification
    with col2:
        with st.form("vendor_otp_verification"):
            st.subheader("Step 2: Verify OTP")
            verify_phone = st.text_input("Phone Number", key="vendor_verify_phone")
            otp = st.text_input("OTP", key="vendor_otp")
            verify_otp = st.form_submit_button("Verify OTP")
            
            if verify_otp and verify_phone and otp:
                with st.spinner("Verifying OTP..."):
                    try:
                        response = requests.post(
                            f"{API_BASE_URL}/users/auth/vendor/",
                            json={"action": "verify_otp", "phone_number": verify_phone, "otp": otp}
                        )
                        result = response.json()
                        
                        # Save tokens if successful
                        if "tokens" in result:
                            st.session_state.access_token = result["tokens"]["access"]
                            st.session_state.refresh_token = result["tokens"]["refresh"]
                            if "user" in result and "id" in result["user"]:
                                st.session_state.user_id = result["user"]["id"]
                            st.success("Authentication successful! Tokens saved.")
                        
                        st.write("Response:")
                        st.json(result)
                    except Exception as e:
                        st.error(f"Error: {str(e)}")

# Admin Login Tab
with tabs[2]:
    st.header("Admin Authentication")
    
    with st.form("admin_login"):
        st.subheader("Admin Login")
        username = st.text_input("Username")
        password = st.text_input("Password", type="password")
        login = st.form_submit_button("Login")
        
        if login and username and password:
            with st.spinner("Logging in..."):
                try:
                    response = requests.post(
                        f"{API_BASE_URL}/users/auth/admin/",
                        json={"username": username, "password": password}
                    )
                    result = response.json()
                    
                    # Save tokens if successful
                    if "tokens" in result:
                        st.session_state.access_token = result["tokens"]["access"]
                        st.session_state.refresh_token = result["tokens"]["refresh"]
                        if "user" in result and "id" in result["user"]:
                            st.session_state.user_id = result["user"]["id"]
                        st.success("Authentication successful! Tokens saved.")
                    
                    st.write("Response:")
                    st.json(result)
                except Exception as e:
                    st.error(f"Error: {str(e)}")

# Test authenticated endpoint
if st.session_state.access_token:
    st.header("Test Authentication")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("Get My User Profile"):
            with st.spinner("Fetching user profile..."):
                try:
                    headers = {"Authorization": f"Bearer {st.session_state.access_token}"}
                    response = requests.get(
                        f"{API_BASE_URL}/users/users/me/",
                        headers=headers
                    )
                    
                    if response.status_code == 200:
                        st.success("Successfully authenticated!")
                        st.json(response.json())
                    else:
                        st.error(f"Authentication failed: {response.status_code}")
                        st.json(response.json())
                except Exception as e:
                    st.error(f"Error: {str(e)}")
    
    with col2:
        if st.button("Get My Customer Profile"):
            with st.spinner("Fetching customer profile..."):
                try:
                    headers = {"Authorization": f"Bearer {st.session_state.access_token}"}
                    response = requests.get(
                        f"{API_BASE_URL}/customers/customers/me/",
                        headers=headers
                    )
                    
                    if response.status_code == 200:
                        st.success("Successfully retrieved customer profile!")
                        st.json(response.json())
                    else:
                        st.error(f"Failed to retrieve customer profile: {response.status_code}")
                        st.json(response.json())
                except Exception as e:
                    st.error(f"Error: {str(e)}")
