import streamlit as st
import requests
import json
import pandas as pd
from datetime import datetime

# Set the API base URL
API_BASE_URL = "http://localhost:8000/api/v1"

st.set_page_config(
    page_title="DODO BOOKER Customer Testing",
    page_icon="🧪",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state for tokens
if "access_token" not in st.session_state:
    st.session_state.access_token = ""
if "refresh_token" not in st.session_state:
    st.session_state.refresh_token = ""
if "user_id" not in st.session_state:
    st.session_state.user_id = ""
if "customer_id" not in st.session_state:
    st.session_state.customer_id = ""

# Sidebar for navigation
st.sidebar.title("DODO BOOKER Testing")
section = st.sidebar.radio(
    "Select Section",
    ["Authentication", "Profile Management", "Address Management", "Wallet Management"]
)

# Display token status
token_status = "✅ Authenticated" if st.session_state.access_token else "❌ Not Authenticated"
st.sidebar.write(f"Status: {token_status}")

if st.session_state.access_token:
    if st.sidebar.button("Logout"):
        st.session_state.access_token = ""
        st.session_state.refresh_token = ""
        st.session_state.user_id = ""
        st.session_state.customer_id = ""
        st.experimental_rerun()

# Helper function to make authenticated requests
def api_request(method, endpoint, data=None, params=None, files=None):
    headers = {}
    if st.session_state.access_token:
        headers["Authorization"] = f"Bearer {st.session_state.access_token}"

    headers["Content-Type"] = "application/json"
    url = f"{API_BASE_URL}/{endpoint}"

    # Debug info in sidebar
    with st.sidebar.expander("Last API Request"):
        st.write(f"**Method:** {method}")
        st.write(f"**URL:** {url}")
        st.write(f"**Headers:** {headers}")
        if data:
            st.write(f"**Data:** {data}")
        if params:
            st.write(f"**Params:** {params}")

    try:
        if method == "GET":
            response = requests.get(url, headers=headers, params=params)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data, params=params, files=files)
        elif method == "PUT":
            response = requests.put(url, headers=headers, json=data, params=params)
        elif method == "PATCH":
            response = requests.patch(url, headers=headers, json=data, params=params)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers)

        # Debug response in sidebar
        with st.sidebar.expander("Last API Response"):
            st.write(f"**Status:** {response.status_code}")
            st.write(f"**Headers:** {dict(response.headers)}")
            try:
                st.write("**Body:**")
                st.json(response.json())
            except:
                st.write(f"**Raw Body:** {response.text[:500]}")

        if response.status_code == 401:
            st.error("Authentication failed. Please log in again.")
            st.session_state.access_token = ""
            return None

        return response
    except Exception as e:
        st.error(f"API Error: {str(e)}")
        with st.sidebar.expander("API Error Details"):
            st.exception(e)
        return None

# Authentication Section
if section == "Authentication":
    st.title("Authentication")

    tabs = st.tabs(["Customer Login"])

    # Customer Login Tab
    with tabs[0]:
        st.header("Customer Authentication")

        col1, col2 = st.columns(2)

        # OTP Generation
        with col1:
            with st.form("customer_otp_generation"):
                st.subheader("Step 1: Generate OTP")
                phone_number = st.text_input("Phone Number")
                generate_otp = st.form_submit_button("Generate OTP")

                if generate_otp and phone_number:
                    with st.spinner("Generating OTP..."):
                        response = api_request(
                            "POST",
                            "users/auth/customer/",
                            data={"action": "generate_otp", "phone_number": phone_number}
                        )
                        if response:
                            result = response.json()
                            st.write("Response:")
                            st.json(result)

                            # For testing, extract OTP if available
                            if "otp" in result and result["otp"]:
                                st.success(f"OTP for testing: {result['otp']}")

        # OTP Verification
        with col2:
            with st.form("customer_otp_verification"):
                st.subheader("Step 2: Verify OTP")
                verify_phone = st.text_input("Phone Number", key="verify_phone")
                otp = st.text_input("OTP")
                verify_otp = st.form_submit_button("Verify OTP")

                if verify_otp and verify_phone and otp:
                    with st.spinner("Verifying OTP..."):
                        response = api_request(
                            "POST",
                            "users/auth/customer/",
                            data={"action": "verify_otp", "phone_number": verify_phone, "otp": otp}
                        )
                        if response:
                            result = response.json()

                            # Save tokens if successful
                            if "tokens" in result:
                                st.session_state.access_token = result["tokens"]["access"]
                                st.session_state.refresh_token = result["tokens"]["refresh"]
                                if "user" in result and "id" in result["user"]:
                                    st.session_state.user_id = result["user"]["id"]
                                st.success("Authentication successful! Tokens saved.")

                            st.write("Response:")
                            st.json(result)

# Profile Management Section
elif section == "Profile Management":
    st.title("Customer Profile Management")

    if not st.session_state.access_token:
        st.warning("Please authenticate first")
    else:
        tabs = st.tabs(["View Profile", "Update Profile", "Statistics"])

        # View Profile Tab
        with tabs[0]:
            st.header("My Profile")

            if st.button("Get My Profile"):
                with st.spinner("Fetching profile..."):
                    response = api_request("GET", "customers/customers/me/")
                    if response and response.status_code == 200:
                        profile_data = response.json()
                        st.session_state.customer_id = profile_data.get("id")

                        # Display profile information
                        col1, col2 = st.columns(2)
                        with col1:
                            st.subheader("Basic Information")
                            st.write(f"**Name:** {profile_data.get('first_name', '')} {profile_data.get('last_name', '')}")
                            st.write(f"**Email:** {profile_data.get('email', '')}")
                            st.write(f"**Phone:** {profile_data.get('phone_number', '')}")
                            st.write(f"**Customer Code:** {profile_data.get('customer_code', '')}")
                            st.write(f"**Status:** {profile_data.get('status', '')}")
                            st.write(f"**Verified:** {'Yes' if profile_data.get('is_verified', False) else 'No'}")

                        with col2:
                            st.subheader("Additional Information")
                            if "wallet" in profile_data and profile_data["wallet"]:
                                st.write(f"**Wallet Balance:** ₹{profile_data['wallet'].get('balance', 0)}")

                            if "preferences" in profile_data and profile_data["preferences"]:
                                st.write(f"**Language:** {profile_data['preferences'].get('preferred_language', '')}")
                                st.write("**Communication Preferences:**")
                                st.write(f"- Email: {'Yes' if profile_data['preferences'].get('communication_email', False) else 'No'}")
                                st.write(f"- SMS: {'Yes' if profile_data['preferences'].get('communication_sms', False) else 'No'}")
                                st.write(f"- Push: {'Yes' if profile_data['preferences'].get('communication_push', False) else 'No'}")

                        # Show full response in expander
                        with st.expander("View Full Response"):
                            st.json(profile_data)
                    else:
                        st.error("Failed to retrieve profile")

        # Update Profile Tab
        with tabs[1]:
            st.header("Update Profile")

            # First get current profile
            response = api_request("GET", "customers/customers/me/")
            if response and response.status_code == 200:
                profile_data = response.json()

                with st.form("update_profile"):
                    st.subheader("Update Profile Information")

                    first_name = st.text_input("First Name", value=profile_data.get("first_name", ""))
                    last_name = st.text_input("Last Name", value=profile_data.get("last_name", ""))
                    email = st.text_input("Email", value=profile_data.get("email", ""))
                    phone = st.text_input("Phone Number", value=profile_data.get("phone_number", ""), disabled=True)

                    update = st.form_submit_button("Update Profile")

                    if update:
                        with st.spinner("Updating profile..."):
                            update_data = {
                                "first_name": first_name,
                                "last_name": last_name,
                                "email": email
                                # phone_number is read-only, so we don't include it
                            }

                            # Debug information
                            st.write("**Debug Info:**")
                            st.write(f"Token: {st.session_state.access_token[:20]}...")
                            st.write(f"Update Data: {update_data}")

                            response = api_request("PUT", "customers/customers/me/", data=update_data)

                            if response:
                                st.write(f"Status Code: {response.status_code}")
                                try:
                                    response_json = response.json()
                                    st.write("Response JSON:")
                                    st.json(response_json)

                                    if response.status_code == 200:
                                        st.success("Profile updated successfully!")
                                    else:
                                        st.error(f"Failed to update profile: {response_json.get('detail', 'Unknown error')}")
                                except Exception as e:
                                    st.error(f"Error parsing response: {str(e)}")
                                    st.text(response.text)
                            else:
                                st.error("No response received from the server")

        # Statistics Tab
        with tabs[2]:
            st.header("Customer Statistics")

            if st.button("Get My Statistics"):
                with st.spinner("Fetching statistics..."):
                    if st.session_state.customer_id:
                        response = api_request("GET", f"customers/customers/{st.session_state.customer_id}/statistics/")
                        if response and response.status_code == 200:
                            stats = response.json()

                            col1, col2, col3, col4 = st.columns(4)
                            col1.metric("Wallet Balance", f"₹{stats.get('wallet_balance', 0)}")
                            col2.metric("Addresses", stats.get("address_count", 0))
                            col3.metric("Documents", stats.get("document_count", 0))
                            col4.metric("Referrals", stats.get("referral_count", 0))

                            st.json(stats)
                        else:
                            st.error("Failed to retrieve statistics")
                    else:
                        st.error("Customer ID not found. Please view your profile first.")

# Address Management Section
elif section == "Address Management":
    st.title("Address Management")

    if not st.session_state.access_token:
        st.warning("Please authenticate first")
    else:
        tabs = st.tabs(["My Addresses", "Add Address"])

        # My Addresses Tab
        with tabs[0]:
            st.header("My Addresses")

            if st.button("Get My Addresses"):
                with st.spinner("Fetching addresses..."):
                    response = api_request("GET", "customers/addresses/")
                    if response and response.status_code == 200:
                        addresses = response.json()

                        if not addresses:
                            st.info("You don't have any addresses yet. Add one in the 'Add Address' tab.")

                        for address in addresses:
                            with st.expander(f"{address.get('address_type', 'Address').title()} - {address.get('city', '')}"):
                                st.write(f"**Address:** {address.get('address_line1', '')}")
                                if address.get('address_line2'):
                                    st.write(f"{address.get('address_line2', '')}")
                                st.write(f"**City:** {address.get('city', '')}, **State:** {address.get('state', '')}")
                                st.write(f"**Country:** {address.get('country', '')}, **Postal Code:** {address.get('postal_code', '')}")
                                st.write(f"**Default:** {'Yes' if address.get('is_default', False) else 'No'}")

                                col1, col2 = st.columns(2)
                                with col1:
                                    if not address.get('is_default', False):
                                        if st.button(f"Set as Default", key=f"default_{address.get('id')}"):
                                            set_default_response = api_request(
                                                "POST",
                                                f"customers/addresses/{address.get('id')}/set_default/"
                                            )
                                            if set_default_response and set_default_response.status_code == 200:
                                                st.success("Address set as default!")
                                                st.experimental_rerun()
                                            else:
                                                st.error("Failed to set address as default")

                                with col2:
                                    if st.button(f"Delete", key=f"delete_{address.get('id')}"):
                                        delete_response = api_request(
                                            "DELETE",
                                            f"customers/addresses/{address.get('id')}/"
                                        )
                                        if delete_response and delete_response.status_code in [200, 204]:
                                            st.success("Address deleted!")
                                            st.experimental_rerun()
                                        else:
                                            st.error("Failed to delete address")
                    else:
                        st.error("Failed to retrieve addresses")

        # Add Address Tab
        with tabs[1]:
            st.header("Add New Address")

            with st.form("add_address"):
                st.subheader("Add New Address")

                address_type = st.selectbox("Address Type", ["home", "work", "other"])
                address_line1 = st.text_input("Address Line 1")
                address_line2 = st.text_input("Address Line 2 (Optional)")
                city = st.text_input("City")
                state = st.text_input("State")
                country = st.text_input("Country")
                postal_code = st.text_input("Postal Code")
                is_default = st.checkbox("Set as Default Address")

                add_address = st.form_submit_button("Add Address")

                if add_address:
                    with st.spinner("Adding address..."):
                        if not address_line1 or not city or not state or not country or not postal_code:
                            st.error("Please fill in all required fields")
                        else:
                            address_data = {
                                "address_type": address_type,
                                "address_line1": address_line1,
                                "address_line2": address_line2,
                                "city": city,
                                "state": state,
                                "country": country,
                                "postal_code": postal_code,
                                "is_default": is_default
                            }

                            response = api_request("POST", "customers/addresses/", data=address_data)
                            if response and response.status_code == 201:
                                st.success("Address added successfully!")
                                st.json(response.json())
                            else:
                                st.error("Failed to add address")
                                if response:
                                    st.json(response.json())

# Wallet Management Section
elif section == "Wallet Management":
    st.title("Wallet Management")

    if not st.session_state.access_token:
        st.warning("Please authenticate first")
    else:
        tabs = st.tabs(["My Wallet", "Add Money", "Transaction History"])

        # Get wallet info first
        wallet_response = api_request("GET", "customers/wallets/my_wallet/")
        wallet_data = None
        if wallet_response and wallet_response.status_code == 200:
            wallet_data = wallet_response.json()
            st.session_state.wallet_id = wallet_data.get("id")

        # My Wallet Tab
        with tabs[0]:
            st.header("My Wallet")

            if st.button("Refresh Wallet"):
                with st.spinner("Fetching wallet..."):
                    wallet_response = api_request("GET", "customers/wallets/my_wallet/")
                    if wallet_response and wallet_response.status_code == 200:
                        wallet_data = wallet_response.json()
                        st.session_state.wallet_id = wallet_data.get("id")

            if wallet_data:
                col1, col2 = st.columns(2)
                with col1:
                    st.metric("Current Balance", f"₹{wallet_data.get('balance', 0)}")
                with col2:
                    st.write(f"**Currency:** {wallet_data.get('currency', 'INR')}")
                    st.write(f"**Status:** {'Active' if wallet_data.get('is_active', True) else 'Inactive'}")

                with st.expander("View Full Wallet Details"):
                    st.json(wallet_data)
            else:
                st.info("No wallet found or unable to retrieve wallet information.")

        # Add Money Tab
        with tabs[1]:
            st.header("Add Money to Wallet")

            if wallet_data:
                with st.form("add_money"):
                    st.subheader("Add Money")

                    amount = st.number_input("Amount", min_value=1.0, step=1.0)
                    add_money = st.form_submit_button("Add Money")

                    if add_money:
                        with st.spinner("Adding money..."):
                            response = api_request(
                                "POST",
                                f"customers/wallets/{st.session_state.wallet_id}/add_money/",
                                data={"amount": amount}
                            )
                            if response and response.status_code == 200:
                                st.success(f"₹{amount} added to your wallet successfully!")
                                st.json(response.json())
                            else:
                                st.error("Failed to add money")
                                if response:
                                    st.json(response.json())
            else:
                st.warning("Please refresh your wallet information first")

        # Transaction History Tab
        with tabs[2]:
            st.header("Transaction History")

            if st.button("View Transactions"):
                with st.spinner("Fetching transactions..."):
                    response = api_request("GET", "customers/wallet-transactions/")
                    if response and response.status_code == 200:
                        transactions = response.json()

                        if not transactions:
                            st.info("No transactions found")
                        else:
                            # Convert to DataFrame for better display
                            df_data = []
                            for tx in transactions:
                                df_data.append({
                                    "Date": tx.get("created_at", "")[:19],  # Truncate to remove milliseconds
                                    "Type": tx.get("transaction_type", "").title(),
                                    "Amount": f"₹{tx.get('amount', 0)}",
                                    "Status": tx.get("status", "").title(),
                                    "Description": tx.get("description", "")
                                })

                            if df_data:
                                df = pd.DataFrame(df_data)
                                st.dataframe(df, use_container_width=True)

                            # Show transaction summary
                            summary_response = api_request("GET", "customers/wallet-transactions/summary/")
                            if summary_response and summary_response.status_code == 200:
                                summary = summary_response.json()

                                st.subheader("Transaction Summary")
                                col1, col2, col3 = st.columns(3)
                                col1.metric("Total Credits", f"₹{summary.get('total_credits', 0)}")
                                col2.metric("Total Debits", f"₹{summary.get('total_debits', 0)}")
                                col3.metric("Transaction Count", summary.get('transaction_count', 0))
                    else:
                        st.error("Failed to retrieve transactions")
