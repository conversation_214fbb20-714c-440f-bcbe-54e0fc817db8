#!/bin/bash
# Script to run both the Django backend and Streamlit testing app

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print header
echo -e "${GREEN}=========================================${NC}"
echo -e "${GREEN}   DODO BOOKER Testing Environment      ${NC}"
echo -e "${GREEN}=========================================${NC}"

# Check if Django server is already running
if lsof -i :8000 > /dev/null; then
    echo -e "${YELLOW}Django server is already running on port 8000${NC}"
else
    echo -e "${YELLOW}Starting Django server on port 8000...${NC}"
    echo -e "${YELLOW}This will run in the background. Check the terminal for any errors.${NC}"
    
    # Start Django server in the background
    cd /Users/<USER>/Desktop/coding/dodopwa/backend
    source venv/bin/activate
    ./runserver.py > django_server.log 2>&1 &
    
    # Save the process ID
    DJANGO_PID=$!
    echo -e "${GREEN}Django server started with PID: ${DJANGO_PID}${NC}"
    
    # Give the server some time to start
    echo -e "${YELLOW}Waiting for Django server to start...${NC}"
    sleep 5
fi

# Start Streamlit app
echo -e "${YELLOW}Starting Streamlit testing app...${NC}"
cd /Users/<USER>/Desktop/coding/dodopwa/testing/streamlit
source /Users/<USER>/Desktop/coding/dodopwa/backend/venv/bin/activate
streamlit run auth_test.py

# Note: When the Streamlit app is closed, this script will continue
# The Django server will keep running in the background
echo -e "${YELLOW}Streamlit app closed. Django server is still running.${NC}"
echo -e "${YELLOW}To stop the Django server, find its process ID and use 'kill <PID>'${NC}"
echo -e "${YELLOW}Or use: lsof -i :8000 | grep LISTEN | awk '{print $2}' | xargs kill${NC}"
