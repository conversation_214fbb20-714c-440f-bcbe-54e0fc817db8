<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PWA Icon Generator</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .container {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
    .form-group {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    label {
      font-weight: bold;
    }
    input, select {
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    button {
      padding: 10px 15px;
      background-color: #4f46e5;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }
    button:hover {
      background-color: #4338ca;
    }
    .preview {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 20px;
    }
    .icon-preview {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 5px;
    }
    canvas {
      border: 1px solid #eee;
      border-radius: 8px;
    }
  </style>
</head>
<body>
  <h1>PWA Icon Generator for DODO BOOKER</h1>
  <p>Use this tool to generate the required icons for the PWA. Download each icon and save it to the <code>public/icons</code> directory.</p>
  
  <div class="container">
    <div class="form-group">
      <label for="backgroundColor">Background Color</label>
      <input type="color" id="backgroundColor" value="#4f46e5">
    </div>
    
    <div class="form-group">
      <label for="textColor">Text Color</label>
      <input type="color" id="textColor" value="#ffffff">
    </div>
    
    <div class="form-group">
      <label for="text">Icon Text</label>
      <input type="text" id="text" value="DODO" maxlength="4">
    </div>
    
    <button id="generateBtn">Generate Icons</button>
    
    <div class="preview" id="preview"></div>
  </div>

  <script>
    const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
    
    function generateIcon(size, bgColor, textColor, text) {
      const canvas = document.createElement('canvas');
      canvas.width = size;
      canvas.height = size;
      const ctx = canvas.getContext('2d');
      
      // Background
      ctx.fillStyle = bgColor;
      ctx.fillRect(0, 0, size, size);
      
      // Text
      ctx.fillStyle = textColor;
      ctx.font = `bold ${size / 3}px Arial`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(text, size / 2, size / 2);
      
      return canvas;
    }
    
    function downloadIcon(canvas, size) {
      const link = document.createElement('a');
      link.download = `icon-${size}x${size}.png`;
      link.href = canvas.toDataURL('image/png');
      link.textContent = `Download ${size}x${size}`;
      link.className = 'download-link';
      link.style.marginTop = '5px';
      return link;
    }
    
    document.getElementById('generateBtn').addEventListener('click', () => {
      const bgColor = document.getElementById('backgroundColor').value;
      const textColor = document.getElementById('textColor').value;
      const text = document.getElementById('text').value || 'DODO';
      
      const previewContainer = document.getElementById('preview');
      previewContainer.innerHTML = '';
      
      sizes.forEach(size => {
        const canvas = generateIcon(size, bgColor, textColor, text);
        const downloadLink = downloadIcon(canvas, size);
        
        const iconPreview = document.createElement('div');
        iconPreview.className = 'icon-preview';
        
        const sizeLabel = document.createElement('div');
        sizeLabel.textContent = `${size}x${size}`;
        
        iconPreview.appendChild(sizeLabel);
        iconPreview.appendChild(canvas);
        iconPreview.appendChild(downloadLink);
        
        previewContainer.appendChild(iconPreview);
      });
    });
  </script>
</body>
</html>
