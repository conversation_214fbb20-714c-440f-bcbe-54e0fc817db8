/** @type {import('next').NextConfig} */
const withPWA = require('next-pwa')({
  dest: 'public',
  disable: process.env.NODE_ENV === 'development',
  register: true,
  skipWaiting: true,
});

const nextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '8000',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000',
        pathname: '/**',
      },
    ],
  },
  async rewrites() {
    return [
      // Customer app routes
      {
        source: '/',
        destination: '/customer',
      },
      // API routes - always add trailing slash to destination
      {
        source: '/api/:path*',
        // Always use port 8000 for backend API
        destination: 'http://localhost:8000/api/v1/:path*/',
      },
    ];
  },
};

module.exports = withPWA(nextConfig);
