// This file is used to load the TypeScript configuration files
// It's needed because Next.js doesn't support TypeScript configuration files directly

// Register ts-node to handle TypeScript files
require('ts-node').register({
  compilerOptions: {
    module: 'commonjs',
    target: 'es2017',
    esModuleInterop: true,
  },
});

// Export the TypeScript configuration
module.exports = require('./next.config.ts');
