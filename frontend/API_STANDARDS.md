# API Standards for DODO BOOKER

## URL Format

### Trailing Slashes

All API endpoints **MUST** include trailing slashes. The Django backend is configured with `APPEND_SLASH = False`, which means it doesn't automatically redirect requests without trailing slashes to URLs with trailing slashes.

✅ Correct: `/api/v1/users/auth/customer/`  
❌ Incorrect: `/api/v1/users/auth/customer`

### API Base URL

All API requests should use the `/api` base URL, which is rewritten by Next.js to include the proper API version and backend URL.

## API Configuration

The Next.js configuration in `next.config.js` is set up to rewrite API requests to include the proper version prefix and trailing slashes:

```javascript
{
  source: '/api/:path*',
  destination: process.env.NEXT_PUBLIC_API_URL
    ? `${process.env.NEXT_PUBLIC_API_URL}/api/v1/:path*/`
    : 'http://localhost:8001/api/v1/:path*/',
}
```

## Making API Requests

Always use the `api` instance from `src/lib/api/index.ts` to make API requests. This ensures proper handling of authentication tokens and error responses.

Example:

```typescript
import api from '@/lib/api';

// Correct way to make API requests
const response = await api.post('/users/auth/customer/', {
  action: 'generate_otp',
  phone_number,
});
```

## Common Issues

1. **404 Not Found errors**: If you're getting 404 errors, check if your endpoint includes a trailing slash.
2. **Authentication errors**: Make sure you're using the `api` instance which automatically includes authentication headers.

## API Response Structure

All API responses follow a consistent structure:

### Success Responses

```json
{
  "message": "Success message",
  "data": { ... }
}
```

### Error Responses

```json
{
  "error": "Error message",
  "details": { ... }
}
```

## Authentication Flow

The authentication flow uses MSG91 for OTP verification:

1. Generate OTP: `POST /users/auth/customer/` with `action: 'generate_otp'`
2. Verify OTP: `POST /users/auth/customer/` with `action: 'verify_otp'`
3. Refresh Token: `POST /users/token/refresh/`

## Debugging API Requests

For debugging API requests, check:

1. Browser Network tab to see the actual request URL and response
2. Backend logs to see if the request is reaching the server
3. Frontend console logs for any errors
