import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Combines class names with Tailwind CSS
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Format phone number to display format
 * @param phoneNumber Phone number to format
 * @returns Formatted phone number
 */
export function formatPhoneNumber(phoneNumber: string): string {
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Format based on length
  if (cleaned.length === 10) {
    return `+91 ${cleaned.slice(0, 5)} ${cleaned.slice(5)}`;
  }
  
  // If it's already formatted with country code
  if (cleaned.length === 12 && cleaned.startsWith('91')) {
    return `+${cleaned.slice(0, 2)} ${cleaned.slice(2, 7)} ${cleaned.slice(7)}`;
  }
  
  // Return as is if we can't format it
  return phoneNumber;
}

/**
 * Validate phone number
 * @param phoneNumber Phone number to validate
 * @returns Boolean indicating if phone number is valid
 */
export function isValidPhoneNumber(phoneNumber: string): boolean {
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Check if it's a valid Indian phone number
  if (cleaned.length === 10) {
    return true;
  }
  
  // If it has country code
  if (cleaned.length === 12 && cleaned.startsWith('91')) {
    return true;
  }
  
  return false;
}

/**
 * Validate OTP
 * @param otp OTP to validate
 * @returns Boolean indicating if OTP is valid
 */
export function isValidOTP(otp: string): boolean {
  // Check if OTP is 6 digits
  return /^\d{6}$/.test(otp);
}

/**
 * Format date to display format
 * @param date Date to format
 * @returns Formatted date
 */
export function formatDate(date: string | Date): string {
  return new Date(date).toLocaleDateString('en-IN', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  });
}

/**
 * Format time to display format
 * @param time Time to format
 * @returns Formatted time
 */
export function formatTime(time: string | Date): string {
  return new Date(time).toLocaleTimeString('en-IN', {
    hour: '2-digit',
    minute: '2-digit',
  });
}
