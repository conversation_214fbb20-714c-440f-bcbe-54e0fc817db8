// Color theme inspired by Urban Company and Uber Eats

export const colors = {
  // Primary colors
  primary: {
    50: '#eef2ff',
    100: '#e0e7ff',
    200: '#c7d2fe',
    300: '#a5b4fc',
    400: '#818cf8',
    500: '#6366f1',  // Main primary color
    600: '#4f46e5',  // Darker primary for buttons
    700: '#4338ca',
    800: '#3730a3',
    900: '#312e81',
    950: '#1e1b4b',
  },
  
  // Secondary colors (teal-like Urban Company)
  secondary: {
    50: '#f0fdfa',
    100: '#ccfbf1',
    200: '#99f6e4',
    300: '#5eead4',
    400: '#2dd4bf',
    500: '#14b8a6',  // Main secondary color
    600: '#0d9488',
    700: '#0f766e',
    800: '#115e59',
    900: '#134e4a',
    950: '#042f2e',
  },
  
  // Accent colors (like Uber Eats)
  accent: {
    50: '#fff7ed',
    100: '#ffedd5',
    200: '#fed7aa',
    300: '#fdba74',
    400: '#fb923c',
    500: '#f97316',  // Main accent color
    600: '#ea580c',
    700: '#c2410c',
    800: '#9a3412',
    900: '#7c2d12',
    950: '#431407',
  },
  
  // Neutral colors
  neutral: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
    950: '#0a0a0a',
  },
  
  // Background colors
  background: {
    light: '#ffffff',
    offWhite: '#f9fafb',
    gray: '#f3f4f6',
    dark: '#111827',
  },
  
  // Text colors
  text: {
    primary: '#111827',
    secondary: '#4b5563',
    muted: '#6b7280',
    light: '#ffffff',
  },
  
  // Status colors
  status: {
    success: '#10b981',
    error: '#ef4444',
    warning: '#f59e0b',
    info: '#3b82f6',
  },
};

// Common color combinations for components
export const colorCombinations = {
  // Navbar
  navbar: {
    background: '#ffffff',
    text: colors.text.primary,
    activeText: colors.primary[600],
    border: colors.neutral[200],
  },
  
  // Hero section
  hero: {
    background: colors.primary[600],
    text: '#ffffff',
    buttonBg: '#ffffff',
    buttonText: colors.primary[600],
  },
  
  // Cards
  card: {
    background: '#ffffff',
    border: colors.neutral[200],
    shadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    hoverShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  },
  
  // Buttons
  button: {
    primary: {
      background: colors.primary[600],
      text: '#ffffff',
      hoverBg: colors.primary[700],
      activeBg: colors.primary[800],
    },
    secondary: {
      background: colors.secondary[500],
      text: '#ffffff',
      hoverBg: colors.secondary[600],
      activeBg: colors.secondary[700],
    },
    accent: {
      background: colors.accent[500],
      text: '#ffffff',
      hoverBg: colors.accent[600],
      activeBg: colors.accent[700],
    },
  },
  
  // Sections
  sections: {
    light: {
      background: colors.background.light,
      text: colors.text.primary,
      border: colors.neutral[200],
    },
    gray: {
      background: colors.background.gray,
      text: colors.text.primary,
      border: colors.neutral[300],
    },
    dark: {
      background: colors.primary[900],
      text: colors.text.light,
      border: colors.primary[800],
    },
    accent: {
      background: colors.accent[50],
      text: colors.text.primary,
      border: colors.accent[200],
    },
  },
};

export default colors;
