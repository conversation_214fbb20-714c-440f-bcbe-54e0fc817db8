import api from '.';

export interface Category {
  id: string;
  name: string;
  description: string | null;
  icon: string | null;
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
  subcategories?: SubCategory[];
}

export interface SubCategory {
  id: string;
  category: string;
  name: string;
  description: string | null;
  icon: string | null;
  is_active: boolean;
  display_order: number;
  created_at: string;
  updated_at: string;
}

export const categoryService = {
  getCategories: async (): Promise<Category[]> => {
    try {
      console.log('Fetching categories from API...');
      const response = await api.get('/api/v1/services/categories/');
      console.log('Categories response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching categories:', error);
      return [];
    }
  },

  getCategory: async (id: string): Promise<Category | null> => {
    try {
      const response = await api.get(`/services/categories/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching category ${id}:`, error);
      return null;
    }
  },

  getCategorySubcategories: async (id: string): Promise<SubCategory[]> => {
    try {
      const response = await api.get(
        `/services/categories/${id}/subcategories/`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching subcategories for category ${id}:`, error);
      return [];
    }
  },

  getSubcategories: async (): Promise<SubCategory[]> => {
    try {
      const response = await api.get('/services/subcategories/');
      return response.data;
    } catch (error) {
      console.error('Error fetching subcategories:', error);
      return [];
    }
  },

  getSubcategory: async (id: string): Promise<SubCategory | null> => {
    try {
      const response = await api.get(`/services/subcategories/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching subcategory ${id}:`, error);
      return null;
    }
  },

  // Get all categories with their subcategories
  getCategoriesWithSubcategories: async (): Promise<Category[]> => {
    try {
      const categories = await categoryService.getCategories();

      // Fetch subcategories for each category
      const categoriesWithSubcategories = await Promise.all(
        categories.map(async (category) => {
          const subcategories = await categoryService.getCategorySubcategories(
            category.id
          );
          return {
            ...category,
            subcategories,
          };
        })
      );

      return categoriesWithSubcategories;
    } catch (error) {
      console.error('Error fetching categories with subcategories:', error);
      return [];
    }
  },
};

export default categoryService;
