import api from '.';
import { Service } from '../types/service';

export const serviceService = {
  getServices: async (): Promise<Service[]> => {
    try {
      const response = await api.get('/services/services/');
      return response.data;
    } catch (error) {
      console.error('Error fetching services:', error);
      return [];
    }
  },

  getService: async (id: string): Promise<Service | null> => {
    try {
      const response = await api.get(`/services/services/${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching service ${id}:`, error);
      return null;
    }
  },

  getSubcategoryServices: async (subcategoryId: string): Promise<Service[]> => {
    try {
      const response = await api.get(
        `/services/subcategories/${subcategoryId}/services/`
      );
      return response.data;
    } catch (error) {
      console.error(
        `Error fetching services for subcategory ${subcategoryId}:`,
        error
      );
      return [];
    }
  },

  getFeaturedServices: async (): Promise<Service[]> => {
    try {
      const response = await api.get('/services/services/featured/');
      return response.data;
    } catch (error) {
      console.error('Error fetching featured services:', error);
      return [];
    }
  },

  searchServices: async (
    query: string,
    categoryId?: string,
    subcategoryId?: string
  ): Promise<Service[]> => {
    try {
      let url = `/services/services/search/?q=${query}`;

      if (categoryId) {
        url += `&category=${categoryId}`;
      }

      if (subcategoryId) {
        url += `&subcategory=${subcategoryId}`;
      }

      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error('Error searching services:', error);
      return [];
    }
  },

  // Get service reviews
  getServiceReviews: async (serviceId: string): Promise<any[]> => {
    try {
      const response = await api.get(
        `/services/services/${serviceId}/reviews/`
      );
      return response.data;
    } catch (error) {
      console.error(`Error fetching reviews for service ${serviceId}:`, error);
      return [];
    }
  },

  // Get service FAQs
  getServiceFAQs: async (serviceId: string): Promise<any[]> => {
    try {
      const response = await api.get(`/services/faqs/?service=${serviceId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching FAQs for service ${serviceId}:`, error);
      return [];
    }
  },

  // Get service addons
  getServiceAddons: async (serviceId: string): Promise<any[]> => {
    try {
      const response = await api.get(`/services/addons/?service=${serviceId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching addons for service ${serviceId}:`, error);
      return [];
    }
  },
};

export default serviceService;
