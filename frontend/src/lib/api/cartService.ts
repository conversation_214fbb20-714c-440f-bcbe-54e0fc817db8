import api from '.';
import { Service } from '../types/service';

export interface CartItemAddon {
  id: string;
  addon: {
    id: string;
    name: string;
    description: string;
    price: number;
    is_required: boolean;
  };
  quantity: number;
  created_at: string;
  updated_at: string;
}

export interface CartItem {
  id: string;
  service: Service;
  quantity: number;
  addons: CartItemAddon[];
  created_at: string;
  updated_at: string;
}

export interface Cart {
  id: string;
  customer: string | null;
  session_id: string | null;
  items: CartItem[];
  subtotal: number;
  discount: number;
  total: number;
  created_at: string;
  updated_at: string;
}

export const cartService = {
  getCart: async (): Promise<Cart> => {
    const response = await api.get('/cart/my_cart/');
    return response.data;
  },
  
  addItem: async (serviceId: string, quantity: number = 1): Promise<Cart> => {
    const response = await api.post('/cart/add_item/', {
      service_id: serviceId,
      quantity
    });
    return response.data;
  },
  
  addAddon: async (cartItemId: string, addonId: string, quantity: number = 1): Promise<Cart> => {
    const response = await api.post('/cart/add_addon/', {
      cart_item_id: cartItemId,
      addon_id: addonId,
      quantity
    });
    return response.data;
  },
  
  updateItem: async (itemId: string, quantity: number): Promise<Cart> => {
    const response = await api.post('/cart/update_item/', {
      item_id: itemId,
      quantity
    });
    return response.data;
  },
  
  removeItem: async (itemId: string): Promise<Cart> => {
    const response = await api.post('/cart/remove_item/', {
      item_id: itemId
    });
    return response.data;
  },
  
  removeAddon: async (cartItemId: string, addonId: string): Promise<Cart> => {
    const response = await api.post('/cart/remove_addon/', {
      cart_item_id: cartItemId,
      addon_id: addonId
    });
    return response.data;
  },
  
  clearCart: async (): Promise<Cart> => {
    const response = await api.post('/cart/clear/');
    return response.data;
  },
  
  checkout: async (checkoutData: {
    booking_date: string;
    start_time: string;
    address_id: string;
  }): Promise<any> => {
    const response = await api.post('/cart/checkout/', checkoutData);
    return response.data;
  }
};

export default cartService;
