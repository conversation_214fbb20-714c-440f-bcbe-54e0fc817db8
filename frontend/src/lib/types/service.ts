export interface ServiceImage {
  id: string;
  image: string;
  alt_text: string | null;
  is_primary: boolean;
}

export interface ServiceAddon {
  id: string;
  name: string;
  description: string;
  price: number;
  is_required: boolean;
}

export interface SubCategory {
  id: string;
  name: string;
  category: {
    id: string;
    name: string;
  };
}

export interface Service {
  id: string;
  name: string;
  title?: string; // For compatibility with existing code
  description: string;
  base_price: number;
  price?: number; // For compatibility with existing code
  discounted_price?: number;
  discount_percentage?: number;
  duration_minutes: number;
  duration?: string; // For compatibility with existing code
  average_rating?: number;
  rating?: number; // For compatibility with existing code
  total_ratings: number;
  reviewCount?: number; // For compatibility with existing code
  subcategory: string;
  subcategory_details?: SubCategory;
  is_active: boolean;
  images?: ServiceImage[];
  image?: string; // For compatibility with existing code
  features?: string[];
  bathrooms?: number;
  created_at: string;
  updated_at: string;
}
