import api from '../api';

export interface User {
  id: string;
  username: string;
  phone_number: string;
  email?: string;
  is_active: boolean;
  date_joined: string;
  last_login?: string;
}

export interface AuthTokens {
  access: string;
  refresh: string;
}

export interface AuthResponse {
  tokens: AuthTokens;
  user_type: string;
  user: User;
  user_status?: 'new' | 'existing';
}

export interface OTPResponse {
  message: string;
  otp_token: string;
  expires_at: string;
  otp?: string | null;
}

/**
 * Send OTP to the provided phone number
 * @param phone_number Phone number to send OTP to
 * @returns OTP response with token
 */
export const sendOTP = async (phone_number: string): Promise<OTPResponse> => {
  try {
    console.log('Sending OTP to:', phone_number);
    console.log('API URL:', api.defaults.baseURL);
    const response = await api.post('/users/auth/customer/', {
      action: 'generate_otp',
      phone_number,
    });
    console.log('OTP response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error sending OTP:', error);
    console.error(
      'Error details:',
      error.response?.data,
      error.response?.status
    );
    console.error('Request details:', error.config?.url, error.config?.data);
    throw error;
  }
};

/**
 * Resend OTP to the provided phone number
 * @param phone_number Phone number to resend OTP to
 * @returns OTP response with token
 */
export const resendOTP = async (phone_number: string): Promise<OTPResponse> => {
  try {
    console.log('Resending OTP to:', phone_number);
    console.log('API URL:', api.defaults.baseURL);
    const response = await api.post('/users/auth/customer/', {
      action: 'resend_otp',
      phone_number,
    });
    console.log('Resend OTP response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error resending OTP:', error);
    console.error(
      'Error details:',
      error.response?.data,
      error.response?.status
    );
    console.error('Request details:', error.config?.url, error.config?.data);
    throw error;
  }
};

/**
 * Verify OTP and authenticate user
 * @param phone_number Phone number
 * @param otp OTP code
 * @returns Authentication response with tokens and user data
 */
export const verifyOTP = async (
  phone_number: string,
  otp: string
): Promise<AuthResponse> => {
  try {
    console.log('Verifying OTP:', phone_number, otp);
    console.log('API URL:', api.defaults.baseURL);
    const response = await api.post('/users/auth/customer/', {
      action: 'verify_otp',
      phone_number,
      otp,
    });

    // Debug the response
    console.log('Full verify OTP response:', JSON.stringify(response.data));
    console.log('Verify OTP response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error verifying OTP:', error);
    console.error(
      'Error details:',
      error.response?.data,
      error.response?.status
    );
    console.error('Request details:', error.config?.url, error.config?.data);
    throw error;
  }
};

/**
 * Save authentication data to local storage and cookies
 * @param authResponse Authentication response from API
 */
export const saveAuthData = (authResponse: AuthResponse): void => {
  if (typeof window === 'undefined') return;

  // Save to localStorage
  localStorage.setItem('access_token', authResponse.tokens.access);
  localStorage.setItem('refresh_token', authResponse.tokens.refresh);
  localStorage.setItem('user', JSON.stringify(authResponse.user));
  localStorage.setItem('user_type', authResponse.user_type);

  // Save to cookies for middleware authentication
  document.cookie = `access_token=${authResponse.tokens.access}; path=/; max-age=900`; // 15 minutes
  document.cookie = `refresh_token=${authResponse.tokens.refresh}; path=/; max-age=604800`; // 7 days
  document.cookie = `user_type=${authResponse.user_type}; path=/; max-age=604800`; // 7 days

  console.log('Auth data saved to localStorage and cookies');
};

/**
 * Get current authenticated user from local storage
 * @returns User object or null if not authenticated
 */
export const getCurrentUser = (): User | null => {
  if (typeof window === 'undefined') return null;

  const userJson = localStorage.getItem('user');
  if (!userJson) return null;

  try {
    return JSON.parse(userJson) as User;
  } catch (error) {
    console.error('Failed to parse user data:', error);
    return null;
  }
};

/**
 * Check if user is authenticated
 * @returns Boolean indicating if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  if (typeof window === 'undefined') return false;

  const token = localStorage.getItem('access_token');
  return !!token;
};

/**
 * Logout user by removing auth data from local storage and cookies
 */
export const logout = (): void => {
  if (typeof window === 'undefined') return;

  // Clear localStorage
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('user');
  localStorage.removeItem('user_type');

  // Clear cookies
  document.cookie = 'access_token=; path=/; max-age=0';
  document.cookie = 'refresh_token=; path=/; max-age=0';
  document.cookie = 'user_type=; path=/; max-age=0';

  console.log('Auth data cleared from localStorage and cookies');
};

/**
 * Refresh access token using refresh token
 * @returns New access token
 */
export const refreshToken = async (): Promise<string> => {
  if (typeof window === 'undefined')
    throw new Error('Cannot refresh token on server side');

  const refreshToken = localStorage.getItem('refresh_token');
  if (!refreshToken) throw new Error('No refresh token available');

  try {
    console.log(
      'Refreshing token with:',
      refreshToken.substring(0, 10) + '...'
    );
    const response = await api.post('/users/token/refresh/', {
      refresh: refreshToken,
    });
    console.log('Refresh token response:', response.data);

    const { access } = response.data;
    localStorage.setItem('access_token', access);

    return access;
  } catch (error) {
    console.error('Error refreshing token:', error);
    throw error;
  }
};
