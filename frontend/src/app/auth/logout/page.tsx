'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';

const LogoutPage = () => {
  const router = useRouter();
  const { logout } = useAuth();

  useEffect(() => {
    // Logout the user
    logout();

    // Redirect to login page after a short delay
    // This gives time for the logout animation to show
    setTimeout(() => {
      window.location.href = '/auth/login';
    }, 1500);
  }, [logout]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-6 bg-white p-8 rounded-lg shadow-md text-center">
        <h1 className="text-2xl font-bold text-gray-900">Logging Out</h1>
        <p className="mt-2 text-gray-600">Please wait while we log you out.</p>
        <div className="mt-6 flex justify-center">
          <div className="animate-spin h-8 w-8 border-4 border-indigo-500 rounded-full border-t-transparent"></div>
        </div>
      </div>
    </div>
  );
};

export default LogoutPage;
