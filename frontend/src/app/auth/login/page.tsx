'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import PhoneForm from '@/components/forms/PhoneForm';
import OTPForm from '@/components/forms/OTPForm';
import {
  sendOTP,
  resendOTP,
  verifyOTP,
  saveAuthData,
  OTPResponse,
} from '@/lib/auth';
import { useAuth } from '@/lib/auth/AuthContext';

const LoginPage = () => {
  const router = useRouter();
  const { setUser, setIsLoggedIn, setUserType } = useAuth();

  const [step, setStep] = useState<'phone' | 'otp'>('phone');
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [otpToken, setOtpToken] = useState<string>('');
  const [expiresAt, setExpiresAt] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [successMessage, setSuccessMessage] = useState<string>('');

  const handlePhoneSubmit = async (phone: string) => {
    setIsLoading(true);
    setError('');

    try {
      const response = await sendOTP(phone);
      setPhoneNumber(phone);
      setOtpToken(response.otp_token);
      setExpiresAt(response.expires_at);
      setStep('otp');

      // For development, log the OTP if it's returned
      if (process.env.NODE_ENV === 'development' && response.otp) {
        console.log('Development OTP:', response.otp);
      }
    } catch (error: any) {
      console.error('Failed to send OTP:', error);
      setError(
        error.response?.data?.error || 'Failed to send OTP. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setIsLoading(true);
    setError('');

    try {
      const response = await resendOTP(phoneNumber);
      setOtpToken(response.otp_token);
      setExpiresAt(response.expires_at);

      // For development, log the OTP if it's returned
      if (process.env.NODE_ENV === 'development' && response.otp) {
        console.log('Development OTP:', response.otp);
      }
    } catch (error: any) {
      console.error('Failed to resend OTP:', error);
      setError(
        error.response?.data?.error || 'Failed to resend OTP. Please try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOTP = async (otp: string) => {
    setIsLoading(true);
    setError('');

    try {
      console.log('Verifying OTP in login page:', otp);
      console.log('Phone number:', phoneNumber);

      const response = await verifyOTP(phoneNumber, otp);
      console.log('Login page received response:', response);
      console.log('User status:', response.user_status);
      console.log('User type:', response.user_type);

      // Save auth data to local storage
      saveAuthData(response);

      // Update auth context
      setUser(response.user);
      setIsLoggedIn(true);
      setUserType(response.user_type);

      console.log('Auth context updated, redirecting user...');

      // Show success message before redirecting
      setError(''); // Clear any existing errors

      // Set success message
      setSuccessMessage(
        response.user_status === 'new'
          ? 'Verification successful! Setting up your profile...'
          : 'Verification successful! Redirecting to your dashboard...'
      );

      // Show success message for a brief moment before redirecting
      console.log('Setting up redirect after authentication...');

      // Check for redirect parameter in URL
      const urlParams = new URLSearchParams(window.location.search);
      const redirectParam = urlParams.get('redirect');

      // Determine where to redirect the user
      let redirectUrl;

      if (redirectParam) {
        // If there's a redirect parameter, use it
        redirectUrl = decodeURIComponent(redirectParam);
        console.log('Redirecting to specified URL:', redirectUrl);
      } else if (response.user_status === 'new') {
        // New user - redirect to profile completion
        redirectUrl = '/customer/profile/complete';
        console.log('New user - redirecting to profile completion');
      } else {
        // Existing user with no redirect - go to dashboard
        redirectUrl = '/customer';
        console.log('Existing user - redirecting to dashboard');
      }

      // Set a flag in sessionStorage to indicate we're in the middle of a redirect
      sessionStorage.setItem('redirecting_after_auth', 'true');
      sessionStorage.setItem('redirect_destination', redirectUrl);

      // Force a direct navigation after a short delay
      setTimeout(() => {
        console.log('Executing redirect to:', redirectUrl);
        try {
          // Use direct navigation methods
          window.location.assign(redirectUrl);
        } catch (error) {
          console.error('Navigation error:', error);
          // Fallback
          document.location.href = redirectUrl;
        }
      }, 800);
    } catch (error: any) {
      console.error('Failed to verify OTP:', error);
      setError(error.response?.data?.error || 'Invalid OTP. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md">
        {step === 'phone' ? (
          <PhoneForm
            onSubmit={handlePhoneSubmit}
            isLoading={isLoading}
            error={error}
          />
        ) : (
          <OTPForm
            phoneNumber={phoneNumber}
            onVerify={handleVerifyOTP}
            onResend={handleResendOTP}
            isLoading={isLoading}
            error={error}
            successMessage={successMessage}
            expiresAt={expiresAt}
          />
        )}
      </div>
    </div>
  );
};

export default LoginPage;
