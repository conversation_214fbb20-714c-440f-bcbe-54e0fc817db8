'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';

export default function LandingPage() {
  const router = useRouter();
  const { isLoggedIn, userType, loading } = useAuth();

  // Redirect all users to customer home page by default
  useEffect(() => {
    // Short delay to ensure smooth transition
    const redirectTimer = setTimeout(() => {
      router.replace('/customer');
    }, 500);

    return () => clearTimeout(redirectTimer);
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-indigo-50">
      <div className="text-center">
        <div className="animate-spin h-12 w-12 border-4 border-indigo-500 rounded-full border-t-transparent mx-auto mb-4"></div>
        <h1 className="text-2xl font-bold text-indigo-700">
          Welcome to DODO BOOKER
        </h1>
        <p className="text-gray-600 mt-2">Redirecting you to our services...</p>
      </div>
    </div>
  );
}
