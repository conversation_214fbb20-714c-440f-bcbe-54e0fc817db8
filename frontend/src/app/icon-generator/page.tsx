'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function IconGeneratorPage() {
  const [bgColor, setBgColor] = useState('#4f46e5');
  const [textColor, setTextColor] = useState('#ffffff');
  const [text, setText] = useState('DODO');
  const [icons, setIcons] = useState<{ size: number; canvas: HTMLCanvasElement | null }[]>([]);
  const router = useRouter();

  const sizes = [72, 96, 128, 144, 152, 192, 384, 512];

  useEffect(() => {
    // Initialize icons array
    setIcons(sizes.map(size => ({ size, canvas: null })));
  }, []);

  const generateIcons = () => {
    const newIcons = sizes.map(size => {
      const canvas = document.createElement('canvas');
      canvas.width = size;
      canvas.height = size;
      const ctx = canvas.getContext('2d');
      
      if (ctx) {
        // Background
        ctx.fillStyle = bgColor;
        ctx.fillRect(0, 0, size, size);
        
        // Text
        ctx.fillStyle = textColor;
        ctx.font = `bold ${size / 3}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(text, size / 2, size / 2);
      }
      
      return { size, canvas };
    });
    
    setIcons(newIcons);
  };

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">PWA Icon Generator for DODO BOOKER</h1>
      <p className="mb-8 text-gray-600">
        Use this tool to generate the required icons for the PWA. Download each icon and save it to the <code className="bg-gray-100 px-2 py-1 rounded">public/icons</code> directory.
      </p>
      
      <div className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="space-y-2">
            <label htmlFor="backgroundColor" className="block font-medium">
              Background Color
            </label>
            <input
              type="color"
              id="backgroundColor"
              value={bgColor}
              onChange={(e) => setBgColor(e.target.value)}
              className="w-full h-10 rounded border"
            />
          </div>
          
          <div className="space-y-2">
            <label htmlFor="textColor" className="block font-medium">
              Text Color
            </label>
            <input
              type="color"
              id="textColor"
              value={textColor}
              onChange={(e) => setTextColor(e.target.value)}
              className="w-full h-10 rounded border"
            />
          </div>
          
          <div className="space-y-2">
            <label htmlFor="text" className="block font-medium">
              Icon Text
            </label>
            <input
              type="text"
              id="text"
              value={text}
              onChange={(e) => setText(e.target.value)}
              maxLength={4}
              className="w-full h-10 px-3 rounded border"
            />
          </div>
        </div>
        
        <button
          onClick={generateIcons}
          className="bg-indigo-600 text-white px-6 py-3 rounded-md hover:bg-indigo-700 transition-colors"
        >
          Generate Icons
        </button>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-8">
          {icons.map(({ size, canvas }) => (
            <div key={size} className="flex flex-col items-center">
              <p className="text-sm font-medium mb-2">{size}x{size}</p>
              {canvas && (
                <>
                  <div className="border border-gray-200 rounded-lg p-2 mb-2">
                    {canvas}
                  </div>
                  <a
                    href={canvas.toDataURL('image/png')}
                    download={`icon-${size}x${size}.png`}
                    className="text-indigo-600 hover:text-indigo-800 text-sm"
                  >
                    Download {size}x{size}
                  </a>
                </>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
