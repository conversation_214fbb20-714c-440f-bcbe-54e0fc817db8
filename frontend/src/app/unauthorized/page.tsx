'use client';

import React from 'react';
import Link from 'next/link';
import { useAuth } from '@/lib/auth/AuthContext';
import { Button } from '@/components/ui/Button';

export default function UnauthorizedPage() {
  const { userType } = useAuth();
  
  // Determine where to redirect based on user type
  const getRedirectPath = () => {
    switch (userType) {
      case 'customer':
        return '/customer';
      case 'vendor':
        return '/vendor';
      case 'admin':
        return '/admin';
      default:
        return '/';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md text-center">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100">
            <svg
              className="h-8 w-8 text-red-600"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
          <h2 className="mt-4 text-2xl font-bold text-gray-900">Unauthorized Access</h2>
          <p className="mt-2 text-gray-600">
            You don't have permission to access this page.
          </p>
        </div>
        
        <div className="mt-6 flex flex-col space-y-4">
          <Link href={getRedirectPath()}>
            <Button variant="brand" size="full">
              Go to Dashboard
            </Button>
          </Link>
          
          <Link href="/">
            <Button variant="outline" size="full">
              Go to Home
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
