'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import CategorySidebar from '@/components/category/CategorySidebar';
import ServiceList from '@/components/service/ServiceList';
import CartSidebar from '@/components/cart/CartSidebar';
import Layout from '@/components/layout/Layout';
import { Service } from '@/lib/types/service';
import {
  categoryService,
  Category,
  SubCategory,
} from '@/lib/api/categoryService';
import { serviceService } from '@/lib/api/serviceService';

// Minimal fallback data for categories in case API fails
const fallbackCategories = [
  {
    id: 'cleaning',
    name: 'Cleaning',
    icon: '/icons/cleaning.svg',
    subCategories: [
      {
        id: 'bathroom-cleaning',
        name: 'Bathroom Cleaning',
        icon: '/icons/bathroom.svg',
        serviceCount: 5,
      },
    ],
  },
];

// Fallback data for services
const fallbackServices: Record<string, Service[]> = {
  'bathroom-cleaning': [
    {
      id: 'classic-cleaning-2',
      name: 'Classic cleaning (2 bathrooms)',
      title: 'Classic cleaning (2 bathrooms)',
      description: 'Standard bathroom cleaning service',
      base_price: 978,
      price: 978,
      discounted_price: 905,
      discountedPrice: 905,
      average_rating: 4.82,
      rating: 4.82,
      total_ratings: 1340,
      reviewCount: 1340,
      duration_minutes: 120,
      duration: '2 hrs',
      image: '/images/services/bathroom-cleaning-1.jpg',
      bathrooms: 2,
      subcategory: 'bathroom-cleaning',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: 'intense-cleaning-2',
      name: 'Intense cleaning (2 bathrooms)',
      title: 'Intense cleaning (2 bathrooms)',
      description: 'Deep bathroom cleaning service',
      base_price: 1098,
      price: 1098,
      discounted_price: 943,
      discountedPrice: 943,
      average_rating: 4.78,
      rating: 4.78,
      total_ratings: 940,
      reviewCount: 940,
      duration_minutes: 160,
      duration: '2 hrs 40 mins',
      image: '/images/services/bathroom-cleaning-2.jpg',
      bathrooms: 2,
      features: ['Floor & tile cleaning with a scrub machine'],
      subcategory: 'bathroom-cleaning',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
  ],
};

interface CartItem {
  service: Service;
  quantity: number;
}

const ServicePage = () => {
  const params = useParams();
  const categoryId = params.categoryId as string;
  const subCategoryId = params.subCategoryId as string;

  const [services, setServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<any[]>(fallbackCategories);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch categories
        const categoriesData =
          await categoryService.getCategoriesWithSubcategories();
        if (categoriesData.length > 0) {
          // Transform the data to match our component's expected format
          const transformedCategories = categoriesData.map((category) => {
            // Process the category icon
            const categoryIconUrl = category.icon
              ? category.icon.startsWith('http')
                ? category.icon
                : `http://localhost:8000${category.icon}`
              : `/icons/${category.name
                  .toLowerCase()
                  .replace(/\s+/g, '-')}.svg`;

            return {
              id: category.id,
              name: category.name,
              icon: categoryIconUrl,
              subCategories: (category.subcategories || []).map(
                (subcategory) => {
                  // Process the subcategory icon
                  const subcategoryIconUrl = subcategory.icon
                    ? subcategory.icon.startsWith('http')
                      ? subcategory.icon
                      : `http://localhost:8000${subcategory.icon}`
                    : `/icons/${subcategory.name
                        .toLowerCase()
                        .replace(/\s+/g, '-')}.svg`;

                  return {
                    id: subcategory.id,
                    name: subcategory.name,
                    icon: subcategoryIconUrl,
                    serviceCount: 0, // Will be updated when we fetch services
                  };
                }
              ),
            };
          });
          setCategories(transformedCategories);
        }

        // Fetch services for the selected subcategory
        const servicesData = await serviceService.getSubcategoryServices(
          subCategoryId
        );
        if (servicesData.length > 0) {
          // Transform services to ensure compatibility with both API and fallback data
          const transformedServices = servicesData.map((service) => ({
            ...service,
            title: service.name,
            rating: service.average_rating,
            reviewCount: service.total_ratings,
            duration: `${service.duration_minutes} mins`,
            price: service.base_price,
            discountedPrice: service.discounted_price,
            image:
              service.images && service.images.length > 0
                ? service.images[0].image.startsWith('http')
                  ? service.images[0].image
                  : `http://localhost:8000${service.images[0].image}`
                : `/images/services/${service.name
                    .toLowerCase()
                    .replace(/\s+/g, '-')}.jpg`,
          }));
          setServices(transformedServices);
        } else {
          // Use fallback data if no services are returned
          setServices(fallbackServices[subCategoryId] || []);
        }

        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Using fallback data.');
        setServices(fallbackServices[subCategoryId] || []);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [subCategoryId]);

  const handleSelectSubCategory = (catId: string, subCatId: string) => {
    // Navigate to the selected subcategory
    window.location.href = `/services/${catId}/${subCatId}`;
  };

  // Find the selected category and subcategory names
  const selectedCategory = categories.find((c) => c.id === categoryId);
  const selectedSubCategory = selectedCategory?.subCategories.find(
    (sc) => sc.id === subCategoryId
  );

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <h1 className="text-3xl font-bold mb-2 text-gray-900">
            {selectedSubCategory?.name || 'Services'}
          </h1>
          {loading ? (
            <div className="h-4 bg-gray-200 rounded w-40 animate-pulse"></div>
          ) : (
            <p className="text-gray-600">
              {services.length} services available
            </p>
          )}
          {error && (
            <p className="text-red-500 text-sm mt-2">
              {error}{' '}
              <span className="text-gray-500">Using fallback data.</span>
            </p>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left sidebar - Categories */}
          <div className="lg:col-span-1">
            <CategorySidebar
              categories={categories}
              selectedCategoryId={categoryId}
              selectedSubCategoryId={subCategoryId}
              onSelectSubCategory={handleSelectSubCategory}
            />
          </div>

          {/* Main content - Services */}
          <div className="lg:col-span-2">
            {loading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className="bg-white rounded-lg shadow-md p-4 animate-pulse"
                  >
                    <div className="flex flex-row h-full">
                      <div className="relative w-1/3 bg-gray-200 h-40"></div>
                      <div className="p-4 flex-1 flex flex-col justify-between">
                        <div>
                          <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                          <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                        </div>
                        <div className="flex justify-between items-center mt-auto">
                          <div className="h-6 bg-gray-200 rounded w-16"></div>
                          <div className="flex space-x-2">
                            <div className="h-8 bg-gray-200 rounded w-16"></div>
                            <div className="h-8 bg-gray-200 rounded w-16"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <ServiceList services={services} />
            )}
          </div>

          {/* Right sidebar - Cart */}
          <div className="lg:col-span-1">
            <CartSidebar />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ServicePage;
