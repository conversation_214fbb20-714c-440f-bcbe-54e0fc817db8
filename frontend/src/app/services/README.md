# Services Pages

This directory contains the pages for displaying services in the DODO BOOKER application.

## Pages

### [categoryId]/[subCategoryId]/page.tsx

This page displays services for a specific subcategory. It includes:

- A sidebar on the left showing categories and subcategories
- A list of services in the center
- A cart sidebar on the right

## URL Structure

The URL structure for services is:

```
/services/:categoryId/:subCategoryId
```

For example:
- `/services/cleaning/bathroom-cleaning`
- `/services/salon/haircut`
- `/services/appliance-repair/ac-repair`

## Layout

The page uses a three-column layout:

1. **Left Column**: CategorySidebar component showing categories and subcategories
2. **Middle Column**: ServiceList component showing services for the selected subcategory
3. **Right Column**: CartSidebar component showing the cart

On mobile devices, the layout stacks vertically.

## User Flow

1. User selects a category and subcategory from the home page
2. User is redirected to the services page for that subcategory
3. User can browse services, add them to the cart, and view cart details
4. User can select a different subcategory from the sidebar without going back to the home page
5. User can proceed to checkout from the cart sidebar

## Data Fetching

In a real application, the page would fetch data from an API based on the categoryId and subCategoryId parameters. For now, it uses mock data.

## Example Usage

```tsx
// URL: /services/cleaning/bathroom-cleaning

// This would render:
// - CategorySidebar with "Cleaning" category expanded and "Bathroom Cleaning" subcategory selected
// - ServiceList showing bathroom cleaning services
// - CartSidebar showing the user's cart
```
