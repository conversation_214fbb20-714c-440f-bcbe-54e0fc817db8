'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { useCart } from '@/lib/cart/CartContext';
import Layout from '@/components/layout/Layout';
import CategorySection from '@/components/category/CategorySection';
import VerticalServiceCard from '@/components/service/VerticalServiceCard';
import { categoryService } from '@/lib/api/categoryService';

// Mock data for popular services
const popularServices = [
  {
    id: '101',
    name: 'Full Home Cleaning',
    price: 1499,
    category: 'Home Cleaning',
    icon: '🧹',
    rating: 4.8,
    reviews: 1245,
  },
  {
    id: '102',
    name: 'Bathroom Cleaning',
    price: 699,
    category: 'Home Cleaning',
    icon: '🧹',
    rating: 4.7,
    reviews: 987,
  },
  {
    id: '103',
    name: 'AC Repair',
    price: 899,
    category: 'Appliance Repair',
    icon: '❄️',
    rating: 4.9,
    reviews: 756,
  },
  {
    id: '104',
    name: 'Refrigerator Repair',
    price: 799,
    category: 'Appliance Repair',
    icon: '🧊',
    rating: 4.6,
    reviews: 543,
  },
  {
    id: '105',
    name: 'Plumbing Services',
    price: 599,
    category: 'Plumbing',
    icon: '🚿',
    rating: 4.7,
    reviews: 876,
  },
  {
    id: '106',
    name: 'Electrical Repairs',
    price: 649,
    category: 'Electrical',
    icon: '💡',
    rating: 4.8,
    reviews: 654,
  },
  {
    id: '107',
    name: 'House Painting',
    price: 2999,
    category: 'Painting',
    icon: '🎨',
    rating: 4.9,
    reviews: 432,
  },
  {
    id: '108',
    name: 'Pest Control',
    price: 1299,
    category: 'Pest Control',
    icon: '🐜',
    rating: 4.7,
    reviews: 765,
  },
];

// We'll use the CategorySection component which fetches categories from the API

// Transform popular services to match ServiceCard component format
const transformedServices = popularServices.map((service) => ({
  id: service.id,
  title: service.name,
  description: `${service.category} service`,
  price: service.price,
  discountedPrice: Math.round(service.price * 0.9), // 10% discount
  rating: service.rating,
  reviewCount: service.reviews,
  duration: '2 hrs',
  image: `/images/services/${service.category
    .toLowerCase()
    .replace(' ', '-')}-${service.id}.jpg`,
  features: [],
}));

export default function CustomerHomePage() {
  const router = useRouter();
  const { user, isLoggedIn, loading } = useAuth();
  const { addItem, items, totalItems } = useCart();
  const [isClient, setIsClient] = useState(false);
  const [location, setLocation] = useState<string>('');
  const [featuredCategories, setFeaturedCategories] = useState<any[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);

  useEffect(() => {
    setIsClient(true);
    // Simulate getting location
    setLocation('Hyderabad');

    // Fetch featured categories for hero section buttons
    const fetchFeaturedCategories = async () => {
      try {
        const categoriesData = await categoryService.getCategories();
        // Get the first 3 categories for the hero section
        setFeaturedCategories(categoriesData.slice(0, 3));
      } catch (error) {
        console.error('Error fetching featured categories:', error);
        // Fallback data if API fails
        setFeaturedCategories([
          { id: 'cleaning', name: 'Home Cleaning', icon: '🧹' },
          { id: 'appliance', name: 'Appliance Repair', icon: '🔧' },
          { id: 'plumbing', name: 'Plumbing', icon: '🚿' },
        ]);
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchFeaturedCategories();
  }, []);

  // Format price as currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Check if item is in cart
  const isInCart = (id: string) => {
    return items.some((item) => item.id === id);
  };

  return (
    <Layout showSearch={true} showLocationSelector={true}>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-24 mt-0 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-600 to-purple-600 opacity-100"></div>
        <div className="container relative mx-auto px-4 py-8 z-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 drop-shadow-lg">
              Home services, on demand
            </h1>
            <p className="text-xl md:text-2xl mb-10 max-w-3xl mx-auto font-medium">
              Book services from verified professionals in your area
            </p>

            <div className="flex flex-col md:flex-row items-center justify-center gap-5 mt-10">
              {loadingCategories ? (
                // Show loading placeholders
                <>
                  {[1, 2, 3].map((i) => (
                    <div
                      key={i}
                      className="w-full md:w-40 h-12 bg-white/20 rounded-lg animate-pulse"
                    ></div>
                  ))}
                </>
              ) : (
                // Show actual category buttons
                featuredCategories.map((category) => (
                  <Link
                    key={category.id}
                    href={`/services/${category.id}/${
                      category.subcategories?.[0]?.id || 'all'
                    }`}
                  >
                    <Button
                      variant="white"
                      size="lg"
                      className="w-full md:w-auto shadow-lg hover:shadow-xl transition-shadow text-lg font-semibold"
                    >
                      <span className="mr-2">
                        {category.icon ? (
                          <img
                            src={category.icon}
                            alt={category.name}
                            className="w-5 h-5 inline"
                          />
                        ) : (
                          '🔍'
                        )}
                      </span>
                      {category.name}
                    </Button>
                  </Link>
                ))
              )}
            </div>
          </div>
        </div>
      </section>

      <main className="flex-1 bg-white">
        {/* Welcome Message */}
        <div className="container mx-auto p-4 py-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {isLoggedIn
              ? `Welcome back, ${user?.phone_number || 'User'}!`
              : 'Welcome to DODO BOOKER'}
          </h2>
          <p className="text-gray-600">
            Book services from verified professionals in your area. Get started
            by browsing our services below.
          </p>
        </div>

        {/* Service Categories */}
        <section className="container mx-auto p-4 mb-8">
          <h3 className="text-2xl font-bold mb-4 text-gray-900">
            Select a service
          </h3>
          <CategorySection
            onSelectSubCategory={(categoryId, subCategoryId) => {
              router.push(`/services/${categoryId}/${subCategoryId}`);
            }}
          />
        </section>

        {/* Popular Services */}
        <section className="container mx-auto p-4 mb-8">
          <h3 className="text-2xl font-bold mb-6 text-gray-900">
            Popular Services
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {transformedServices.slice(0, 4).map((service) => (
              <VerticalServiceCard key={service.id} service={service} />
            ))}
          </div>
        </section>

        {/* Why Choose Us */}
        <section className="bg-accent-50 py-12">
          <div className="container mx-auto p-4">
            <h3 className="text-2xl font-semibold mb-8 text-center text-gray-900">
              Why Choose DODO BOOKER?
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center text-primary-600 mx-auto mb-4">
                  <span className="text-3xl">👨‍🔧</span>
                </div>
                <h4 className="font-bold mb-2 text-center text-gray-900">
                  Verified Professionals
                </h4>
                <p className="text-gray-600 text-center">
                  All our service providers are verified and trained to deliver
                  quality service.
                </p>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center text-primary-600 mx-auto mb-4">
                  <span className="text-3xl">💰</span>
                </div>
                <h4 className="font-bold mb-2 text-center text-gray-900">
                  Transparent Pricing
                </h4>
                <p className="text-gray-600 text-center">
                  Know the price before you book. No hidden charges or
                  surprises.
                </p>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center text-primary-600 mx-auto mb-4">
                  <span className="text-3xl">🛡️</span>
                </div>
                <h4 className="font-bold mb-2 text-center text-gray-900">
                  Satisfaction Guaranteed
                </h4>
                <p className="text-gray-600 text-center">
                  Not happy with the service? We'll make it right or give you a
                  refund.
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>
    </Layout>
  );
}
