'use client';

import React from 'react';
import { useCart } from '@/lib/cart/CartContext';
import CheckoutAuthGuard from '@/components/checkout/CheckoutAuthGuard';
import { Button } from '@/components/ui/Button';
import Navbar from '@/components/layout/Navbar';

const CheckoutPage = () => {
  const { items, subtotal, totalItems, removeItem, updateQuantity } = useCart();

  // Format price as currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(price);
  };

  // Calculate taxes and total
  const taxes = subtotal * 0.18; // 18% GST
  const total = subtotal + taxes;

  return (
    <CheckoutAuthGuard>
      <div className="flex flex-col min-h-screen">
        <Navbar showSearch={false} showLocationSelector={false} />

        <main className="flex-1 bg-gray-50 py-8">
          <div className="max-w-6xl mx-auto px-4">
            <h1 className="text-3xl font-bold mb-8">Checkout</h1>

            {items.length === 0 ? (
              <div className="text-center py-12">
                <h2 className="text-2xl font-semibold mb-4">
                  Your cart is empty
                </h2>
                <p className="text-gray-600 mb-6">
                  Looks like you haven't added any services to your cart yet.
                </p>
                <Button
                  variant="brand"
                  onClick={() => (window.location.href = '/customer')}
                >
                  Browse Services
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Cart Items */}
                <div className="lg:col-span-2">
                  <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 className="text-xl font-semibold mb-4">
                      Cart Items ({totalItems})
                    </h2>

                    <div className="divide-y">
                      {items.map((item) => (
                        <div key={item.id} className="py-4 flex items-center">
                          {/* Item image */}
                          <div className="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center mr-4">
                            {item.image ? (
                              <img
                                src={item.image}
                                alt={item.name}
                                className="w-full h-full object-cover rounded-md"
                              />
                            ) : (
                              <span className="text-gray-400">No image</span>
                            )}
                          </div>

                          {/* Item details */}
                          <div className="flex-1">
                            <h3 className="font-medium">{item.name}</h3>
                            <p className="text-gray-600">
                              {formatPrice(item.price)} per unit
                            </p>
                          </div>

                          {/* Quantity controls */}
                          <div className="flex items-center">
                            <button
                              className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center"
                              onClick={() =>
                                updateQuantity(item.id, item.quantity - 1)
                              }
                            >
                              -
                            </button>
                            <span className="mx-3">{item.quantity}</span>
                            <button
                              className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center"
                              onClick={() =>
                                updateQuantity(item.id, item.quantity + 1)
                              }
                            >
                              +
                            </button>
                          </div>

                          {/* Item total */}
                          <div className="ml-6 text-right">
                            <p className="font-medium">
                              {formatPrice(item.price * item.quantity)}
                            </p>
                            <button
                              className="text-red-500 text-sm"
                              onClick={() => removeItem(item.id)}
                            >
                              Remove
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Order Summary */}
                <div className="lg:col-span-1">
                  <div className="bg-white rounded-lg shadow-md p-6 sticky top-4">
                    <h2 className="text-xl font-semibold mb-4">
                      Order Summary
                    </h2>

                    <div className="space-y-3 mb-6">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Subtotal</span>
                        <span>{formatPrice(subtotal)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Taxes (18% GST)</span>
                        <span>{formatPrice(taxes)}</span>
                      </div>
                      <div className="border-t pt-3 mt-3">
                        <div className="flex justify-between font-semibold">
                          <span>Total</span>
                          <span>{formatPrice(total)}</span>
                        </div>
                      </div>
                    </div>

                    <Button variant="brand" size="full">
                      Proceed to Payment
                    </Button>

                    <p className="text-xs text-gray-500 mt-4 text-center">
                      By proceeding, you agree to our Terms of Service and
                      Privacy Policy
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>

        <footer className="bg-gray-900 text-white p-6">
          <div className="container mx-auto text-center">
            <p className="text-gray-400">
              &copy; {new Date().getFullYear()} DODO BOOKER. All rights
              reserved.
            </p>
          </div>
        </footer>
      </div>
    </CheckoutAuthGuard>
  );
};

export default CheckoutPage;
