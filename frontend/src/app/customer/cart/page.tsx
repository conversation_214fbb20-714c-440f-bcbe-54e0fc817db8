'use client';

import React from 'react';
import Link from 'next/link';
import { useCart } from '@/lib/cart/CartContext';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/lib/auth/AuthContext';
import Navbar from '@/components/layout/Navbar';

const CartPage = () => {
  const { items, subtotal, totalItems, removeItem, updateQuantity, clearCart } =
    useCart();
  const { isLoggedIn } = useAuth();

  // Format price as currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Calculate taxes and total
  const taxes = subtotal * 0.18; // 18% GST
  const total = subtotal + taxes;

  return (
    <div className="min-h-screen flex flex-col">
      {/* Navbar */}
      <Navbar showSearch={true} showLocationSelector={true} />

      <main className="flex-1 bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          <h1 className="text-2xl font-bold mb-6">
            Your Cart ({totalItems} items)
          </h1>

          {items.length === 0 ? (
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
              <div className="text-5xl mb-4">🛒</div>
              <h2 className="text-xl font-semibold mb-2">Your cart is empty</h2>
              <p className="text-gray-600 mb-6">
                Looks like you haven't added any services to your cart yet.
              </p>
              <Link href="/customer">
                <Button variant="brand" size="lg">
                  Browse Services
                </Button>
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Cart Items */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                  <div className="p-4 border-b">
                    <h2 className="font-semibold">Cart Items</h2>
                  </div>

                  <div className="divide-y">
                    {items.map((item) => (
                      <div key={item.id} className="p-4 flex items-center">
                        {/* Item image/icon */}
                        <div className="w-12 h-12 bg-indigo-100 rounded-md flex items-center justify-center mr-4">
                          <span className="text-2xl">{item.image || '🔧'}</span>
                        </div>

                        {/* Item details */}
                        <div className="flex-1">
                          <h3 className="font-medium">{item.name}</h3>
                          <p className="text-gray-600 text-sm">
                            {formatPrice(item.price)} per unit
                          </p>
                        </div>

                        {/* Quantity controls */}
                        <div className="flex items-center mr-4">
                          <button
                            className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center"
                            onClick={() =>
                              updateQuantity(item.id, item.quantity - 1)
                            }
                          >
                            -
                          </button>
                          <span className="mx-3">{item.quantity}</span>
                          <button
                            className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center"
                            onClick={() =>
                              updateQuantity(item.id, item.quantity + 1)
                            }
                          >
                            +
                          </button>
                        </div>

                        {/* Item total */}
                        <div className="text-right">
                          <p className="font-medium">
                            {formatPrice(item.price * item.quantity)}
                          </p>
                          <button
                            className="text-red-500 text-sm"
                            onClick={() => removeItem(item.id)}
                          >
                            Remove
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Cart actions */}
                  <div className="p-4 bg-gray-50 flex justify-between items-center">
                    <button
                      className="text-red-500 text-sm font-medium"
                      onClick={() => clearCart()}
                    >
                      Clear Cart
                    </button>
                    <Link href="/customer">
                      <button className="text-indigo-600 text-sm font-medium">
                        Continue Shopping
                      </button>
                    </Link>
                  </div>
                </div>
              </div>

              {/* Order Summary */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-lg shadow-md p-6 sticky top-4">
                  <h2 className="font-semibold mb-4">Order Summary</h2>

                  <div className="space-y-3 mb-6">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Subtotal</span>
                      <span>{formatPrice(subtotal)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Taxes (18% GST)</span>
                      <span>{formatPrice(taxes)}</span>
                    </div>
                    <div className="border-t pt-3 mt-3">
                      <div className="flex justify-between font-semibold">
                        <span>Total</span>
                        <span>{formatPrice(total)}</span>
                      </div>
                    </div>
                  </div>

                  <Link href="/customer/checkout">
                    <Button variant="brand" size="full">
                      Proceed to Checkout
                    </Button>
                  </Link>

                  <p className="text-xs text-gray-500 mt-4 text-center">
                    {isLoggedIn
                      ? 'By proceeding, you agree to our Terms of Service and Privacy Policy'
                      : "You'll need to log in before completing your purchase"}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      <footer className="bg-gray-900 text-white p-6">
        <div className="container mx-auto text-center">
          <p className="text-gray-400">
            &copy; {new Date().getFullYear()} DODO BOOKER. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default CartPage;
