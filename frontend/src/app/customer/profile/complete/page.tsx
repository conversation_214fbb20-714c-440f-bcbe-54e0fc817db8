'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import api from '@/lib/api';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

interface ProfileFormData {
  username: string;
  email: string;
  first_name: string;
  last_name: string;
}

export default function ProfileCompletePage() {
  const router = useRouter();
  const { user, setUser } = useAuth();
  const [formData, setFormData] = useState<ProfileFormData>({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Pre-fill form with existing user data
  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username || '',
        email: user.email || '',
        first_name: '',
        last_name: '',
      });
    }
  }, [user]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');

    try {
      // Call API to update profile
      const response = await api.post('/users/profile/customer/', formData);

      console.log('Profile update response:', response.data);

      // Update user in context
      if (user) {
        setUser({
          ...user,
          ...response.data,
        });
      }

      setSuccessMessage('Profile updated successfully!');

      // Redirect to customer dashboard after a delay
      setTimeout(() => {
        router.push('/customer');
      }, 1500);
    } catch (error: any) {
      console.error('Failed to update profile:', error);
      setError(
        error.response?.data?.error ||
          'Failed to update profile. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ProtectedRoute userTypes={['customer']}>
      <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md mx-auto bg-white p-8 rounded-lg shadow">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">
              Complete Your Profile
            </h1>
            <p className="mt-2 text-sm text-gray-600">
              Please provide the following information to complete your profile
            </p>
          </div>

          {successMessage ? (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
              <p className="text-green-700 text-center font-medium">
                {successMessage}
              </p>
              <div className="mt-2 flex justify-center">
                <div className="animate-spin h-5 w-5 border-2 border-green-500 rounded-full border-t-transparent"></div>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <Input
                label="Username"
                name="username"
                type="text"
                value={formData.username}
                onChange={handleChange}
                disabled={true}
                error={error}
              />

              <Input
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Enter your email"
                error={error}
              />

              <Input
                label="First Name"
                name="first_name"
                type="text"
                value={formData.first_name}
                onChange={handleChange}
                placeholder="Enter your first name"
                error={error}
              />

              <Input
                label="Last Name"
                name="last_name"
                type="text"
                value={formData.last_name}
                onChange={handleChange}
                placeholder="Enter your last name"
                error={error}
              />

              <Button
                type="submit"
                variant="brand"
                size="full"
                isLoading={isSubmitting}
                disabled={isSubmitting}
              >
                Complete Profile
              </Button>
            </form>
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
}
