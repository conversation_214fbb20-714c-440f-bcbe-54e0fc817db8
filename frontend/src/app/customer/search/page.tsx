'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { useCart } from '@/lib/cart/CartContext';
import Navbar from '@/components/layout/Navbar';

// Mock service data
const allServices = [
  {
    id: '101',
    name: 'Full Home Cleaning',
    price: 1499,
    category: 'Home Cleaning',
    icon: '🧹',
    rating: 4.8,
    reviews: 1245,
    description: 'Complete cleaning of your home including dusting, mopping, bathroom cleaning, and kitchen cleaning.',
    locations: ['Hyderabad', 'Bangalore', 'Mumbai', 'Delhi', 'Chennai']
  },
  {
    id: '102',
    name: 'Bathroom Cleaning',
    price: 699,
    category: 'Home Cleaning',
    icon: '🧹',
    rating: 4.7,
    reviews: 987,
    description: 'Deep cleaning of bathrooms including toilet, shower, sink, and floor.',
    locations: ['Hyderabad', 'Bangalore', 'Mumbai', 'Delhi']
  },
  {
    id: '103',
    name: 'Kitchen Cleaning',
    price: 799,
    category: 'Home Cleaning',
    icon: '🧹',
    rating: 4.6,
    reviews: 876,
    description: 'Thorough cleaning of kitchen including countertops, sink, appliances, and cabinets.',
    locations: ['Hyderabad', 'Bangalore', 'Mumbai']
  },
  {
    id: '104',
    name: 'AC Repair',
    price: 899,
    category: 'Appliance Repair',
    icon: '❄️',
    rating: 4.9,
    reviews: 756,
    description: 'Diagnosis and repair of air conditioners of all brands.',
    locations: ['Hyderabad', 'Bangalore', 'Mumbai', 'Delhi', 'Chennai']
  },
  {
    id: '105',
    name: 'AC Service',
    price: 599,
    category: 'Appliance Repair',
    icon: '❄️',
    rating: 4.8,
    reviews: 654,
    description: 'Regular maintenance and servicing of air conditioners to ensure optimal performance.',
    locations: ['Hyderabad', 'Bangalore', 'Mumbai', 'Delhi']
  },
  {
    id: '106',
    name: 'Refrigerator Repair',
    price: 799,
    category: 'Appliance Repair',
    icon: '🧊',
    rating: 4.6,
    reviews: 543,
    description: 'Repair services for refrigerators of all brands and types.',
    locations: ['Hyderabad', 'Bangalore', 'Mumbai', 'Delhi', 'Chennai']
  },
  {
    id: '107',
    name: 'Plumbing Services',
    price: 599,
    category: 'Plumbing',
    icon: '🚿',
    rating: 4.7,
    reviews: 876,
    description: 'General plumbing services including leak repairs, pipe installation, and drain cleaning.',
    locations: ['Hyderabad', 'Bangalore', 'Mumbai', 'Delhi', 'Chennai', 'Kolkata', 'Pune']
  },
  {
    id: '108',
    name: 'Pipe Fitting',
    price: 499,
    category: 'Plumbing',
    icon: '🚿',
    rating: 4.6,
    reviews: 765,
    description: 'Installation and replacement of pipes for water supply and drainage.',
    locations: ['Hyderabad', 'Bangalore', 'Mumbai', 'Delhi']
  },
  {
    id: '109',
    name: 'Electrical Repairs',
    price: 649,
    category: 'Electrical',
    icon: '💡',
    rating: 4.8,
    reviews: 654,
    description: 'Repair of electrical issues including switches, outlets, and wiring.',
    locations: ['Hyderabad', 'Bangalore', 'Mumbai', 'Delhi', 'Chennai']
  },
  {
    id: '110',
    name: 'House Painting',
    price: 2999,
    category: 'Painting',
    icon: '🎨',
    rating: 4.9,
    reviews: 432,
    description: 'Complete house painting services including walls, ceilings, and trim.',
    locations: ['Hyderabad', 'Bangalore', 'Mumbai', 'Delhi']
  },
  {
    id: '111',
    name: 'Pest Control',
    price: 1299,
    category: 'Pest Control',
    icon: '🐜',
    rating: 4.7,
    reviews: 765,
    description: 'Comprehensive pest control treatment for cockroaches, ants, and other common pests.',
    locations: ['Hyderabad', 'Bangalore', 'Mumbai', 'Delhi', 'Chennai', 'Kolkata', 'Pune']
  },
  {
    id: '112',
    name: 'Carpentry',
    price: 899,
    category: 'Carpentry',
    icon: '🪑',
    rating: 4.8,
    reviews: 543,
    description: 'Carpentry services including furniture repair, assembly, and custom woodwork.',
    locations: ['Hyderabad', 'Bangalore', 'Mumbai', 'Delhi']
  }
];

const SearchPage = () => {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  const locationParam = searchParams.get('location') || '';
  const { addItem, items } = useCart();
  const [searchResults, setSearchResults] = useState<typeof allServices>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 5000]);
  const [sortBy, setSortBy] = useState<string>('relevance');

  // Format price as currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Check if item is in cart
  const isInCart = (id: string) => {
    return items.some(item => item.id === id);
  };

  // Handle add to cart
  const handleAddToCart = (service: any) => {
    addItem({
      id: service.id,
      name: service.name,
      price: service.price,
      quantity: 1,
      image: service.icon,
    });
  };

  // Get unique categories from all services
  const categories = Array.from(new Set(allServices.map(service => service.category)));

  // Filter and sort services based on search parameters
  useEffect(() => {
    let results = [...allServices];
    
    // Filter by search query
    if (query) {
      const searchTerms = query.toLowerCase().split(' ');
      results = results.filter(service => 
        searchTerms.some(term => 
          service.name.toLowerCase().includes(term) || 
          service.category.toLowerCase().includes(term) ||
          service.description.toLowerCase().includes(term)
        )
      );
    }
    
    // Filter by location
    if (locationParam) {
      results = results.filter(service => 
        service.locations.includes(locationParam)
      );
    }
    
    // Filter by category
    if (selectedCategory) {
      results = results.filter(service => 
        service.category === selectedCategory
      );
    }
    
    // Filter by price range
    results = results.filter(service => 
      service.price >= priceRange[0] && service.price <= priceRange[1]
    );
    
    // Sort results
    switch (sortBy) {
      case 'price_low':
        results.sort((a, b) => a.price - b.price);
        break;
      case 'price_high':
        results.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        results.sort((a, b) => b.rating - a.rating);
        break;
      case 'popularity':
        results.sort((a, b) => b.reviews - a.reviews);
        break;
      default:
        // Default sorting by relevance (keep original order)
        break;
    }
    
    setSearchResults(results);
  }, [query, locationParam, selectedCategory, priceRange, sortBy]);

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar showSearch={true} showLocationSelector={true} />
      
      <main className="flex-1 bg-gray-50 py-6">
        <div className="container mx-auto px-4">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">
              {query 
                ? `Search results for "${query}" in ${locationParam || 'all locations'}`
                : 'All Services'}
            </h1>
            <p className="text-gray-600 mt-1">
              {searchResults.length} services found
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Filters sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-4">
                <h2 className="font-semibold text-lg mb-4">Filters</h2>
                
                {/* Category filter */}
                <div className="mb-6">
                  <h3 className="font-medium mb-2">Category</h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input
                        type="radio"
                        id="category-all"
                        name="category"
                        checked={selectedCategory === ''}
                        onChange={() => setSelectedCategory('')}
                        className="mr-2"
                      />
                      <label htmlFor="category-all">All Categories</label>
                    </div>
                    {categories.map(category => (
                      <div key={category} className="flex items-center">
                        <input
                          type="radio"
                          id={`category-${category}`}
                          name="category"
                          checked={selectedCategory === category}
                          onChange={() => setSelectedCategory(category)}
                          className="mr-2"
                        />
                        <label htmlFor={`category-${category}`}>{category}</label>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Price range filter */}
                <div className="mb-6">
                  <h3 className="font-medium mb-2">Price Range</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>{formatPrice(priceRange[0])}</span>
                      <span>{formatPrice(priceRange[1])}</span>
                    </div>
                    <input
                      type="range"
                      min="0"
                      max="5000"
                      step="100"
                      value={priceRange[1]}
                      onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                      className="w-full"
                    />
                  </div>
                </div>
                
                {/* Sort options */}
                <div>
                  <h3 className="font-medium mb-2">Sort By</h3>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="relevance">Relevance</option>
                    <option value="price_low">Price: Low to High</option>
                    <option value="price_high">Price: High to Low</option>
                    <option value="rating">Rating</option>
                    <option value="popularity">Popularity</option>
                  </select>
                </div>
              </div>
            </div>
            
            {/* Search results */}
            <div className="lg:col-span-3">
              {searchResults.length === 0 ? (
                <div className="bg-white rounded-lg shadow-sm p-8 text-center">
                  <div className="text-5xl mb-4">🔍</div>
                  <h2 className="text-xl font-semibold mb-2">No services found</h2>
                  <p className="text-gray-600 mb-6">
                    Try adjusting your search or filters to find what you're looking for.
                  </p>
                  <Link href="/customer">
                    <Button variant="brand">
                      Browse All Services
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {searchResults.map((service) => (
                    <div
                      key={service.id}
                      className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                    >
                      <div className="p-4">
                        <div className="flex items-start">
                          <div className="w-12 h-12 bg-indigo-100 rounded-md flex items-center justify-center mr-4">
                            <span className="text-2xl">{service.icon}</span>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center text-sm text-gray-500 mb-1">
                              <span>{service.category}</span>
                              <span className="mx-2">•</span>
                              <span className="flex items-center">
                                <span className="text-yellow-400 mr-1">★</span>
                                {service.rating} ({service.reviews})
                              </span>
                            </div>
                            <h3 className="font-medium text-gray-900 mb-1">
                              {service.name}
                            </h3>
                            <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                              {service.description}
                            </p>
                            <div className="flex justify-between items-center">
                              <span className="font-bold text-gray-900">
                                {formatPrice(service.price)}
                              </span>
                              <Button
                                variant={isInCart(service.id) ? "outline" : "brand"}
                                size="sm"
                                onClick={() => handleAddToCart(service)}
                                disabled={isInCart(service.id)}
                              >
                                {isInCart(service.id) ? "Added" : "Add to Cart"}
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
      
      <footer className="bg-gray-900 text-white p-6">
        <div className="container mx-auto text-center">
          <p className="text-gray-400">
            &copy; {new Date().getFullYear()} DODO BOOKER. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default SearchPage;
