import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Paths that don't require authentication
const publicPaths = [
  '/auth/login',
  '/icon-generator.html',
  '/manifest.json',
  '/service-worker.js',
  '/favicon.ico',
  '/',
  '/customer',
  '/customer/services',
  '/customer/cart',
];

// Check if the path is a public path or a static asset
const isPublicPath = (path: string) => {
  if (publicPaths.some((publicPath) => path.startsWith(publicPath))) {
    return true;
  }

  // Static assets
  if (
    path.startsWith('/_next') ||
    path.startsWith('/icons') ||
    path.startsWith('/images') ||
    path.includes('.') // Files with extensions are likely static assets
  ) {
    return true;
  }

  return false;
};

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Allow public paths and static assets
  if (isPublicPath(pathname)) {
    return NextResponse.next();
  }

  // Check for authentication token
  const token = request.cookies.get('access_token')?.value;

  // Define paths that require authentication
  const authRequiredPaths = [
    '/customer/checkout',
    '/customer/profile',
    '/customer/orders',
    '/customer/bookings',
    '/vendor',
    '/admin',
  ];

  // Check if current path requires authentication
  const requiresAuth = authRequiredPaths.some((path) =>
    pathname.startsWith(path)
  );

  // If path requires auth and no token is found, redirect to login
  if (requiresAuth && !token) {
    const url = new URL('/auth/login', request.url);
    url.searchParams.set('redirect', pathname);
    return NextResponse.redirect(url);
  }

  // Only check user type if user is authenticated
  if (token) {
    // Check user type for specific paths
    const userType = request.cookies.get('user_type')?.value;

    // Vendor routes should only be accessible by vendors
    if (pathname.startsWith('/vendor') && userType !== 'vendor') {
      return NextResponse.redirect(new URL('/', request.url));
    }

    // Admin routes should only be accessible by admins
    if (pathname.startsWith('/admin') && userType !== 'admin') {
      return NextResponse.redirect(new URL('/', request.url));
    }

    // Protected customer routes should only be accessible by customers
    if (
      (pathname.startsWith('/customer/profile') ||
        pathname.startsWith('/customer/orders') ||
        pathname.startsWith('/customer/bookings') ||
        pathname.startsWith('/customer/checkout')) &&
      userType !== 'customer'
    ) {
      return NextResponse.redirect(new URL('/', request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * 1. /api routes
     * 2. /_next (Next.js internals)
     * 3. /_static (inside /public)
     * 4. /_vercel (Vercel internals)
     * 5. /favicon.ico, /sitemap.xml, /robots.txt (static files)
     */
    '/((?!api|_next|_static|_vercel|favicon.ico|sitemap.xml|robots.txt).*)',
  ],
};
