# Category and Service Components

This directory contains components for displaying categories, subcategories, and services in the DODO BOOKER application.

## Components

### CategorySection

The main component for displaying service categories on the home page. When a user clicks on a category, it opens a modal showing subcategories.

### SubCategoryModal

A modal component that displays subcategories for a selected category. When a user selects a subcategory, they are redirected to the services page for that subcategory.

### CategorySidebar

A sidebar component used on the services page to display categories and subcategories. It allows users to navigate between different service categories without going back to the home page.

## User Flow

1. User sees categories on the home page
2. User clicks on a category to see subcategories in a modal
3. User selects a subcategory and is redirected to the services page
4. On the services page, the user can:
   - View services for the selected subcategory in the center
   - Browse other categories and subcategories using the sidebar on the left
   - View their cart on the right

## Design Principles

- Categories and subcategories use icons for visual recognition
- Services use images to showcase the actual service
- The UI follows a clean, modern design similar to Urban Company
- The layout is responsive and works well on mobile and desktop

## File Structure

```
components/
├── category/
│   ├── CategorySection.tsx - Main category grid for home page
│   ├── SubCategoryModal.tsx - Modal for selecting subcategories
│   └── CategorySidebar.tsx - Sidebar for services page
├── service/
│   ├── ServiceCard.tsx - Card component for displaying a service
│   └── ServiceList.tsx - Grid of service cards
└── cart/
    └── CartSidebar.tsx - Cart sidebar for services page
```
