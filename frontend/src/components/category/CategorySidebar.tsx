'use client';

import React, { useState } from 'react';
import Image from 'next/image';

interface SubCategory {
  id: string;
  name: string;
  icon: string | null;
  serviceCount: number;
}

interface Category {
  id: string;
  name: string;
  icon: string | null;
  subCategories: SubCategory[];
}

interface CategorySidebarProps {
  categories: Category[];
  selectedCategoryId: string;
  selectedSubCategoryId: string;
  onSelectSubCategory: (categoryId: string, subCategoryId: string) => void;
}

const CategorySidebar: React.FC<CategorySidebarProps> = ({
  categories,
  selectedCategoryId,
  selectedSubCategoryId,
  onSelectSubCategory,
}) => {
  const [expandedCategories, setExpandedCategories] = useState<string[]>([
    selectedCategoryId,
  ]);

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4 sticky top-24">
      <h2 className="text-xl font-bold mb-4 text-gray-900">Categories</h2>

      <div className="space-y-2">
        {categories.map((category) => (
          <div
            key={category.id}
            className="border-b border-gray-100 pb-2 last:border-b-0 last:pb-0"
          >
            <button
              className={`flex items-center justify-between w-full py-2 px-1 rounded-md ${
                selectedCategoryId === category.id
                  ? 'bg-primary-50'
                  : 'hover:bg-gray-50'
              }`}
              onClick={() => toggleCategory(category.id)}
            >
              <div className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center mr-2 ${
                    selectedCategoryId === category.id
                      ? 'bg-primary-100'
                      : 'bg-gray-100'
                  }`}
                >
                  {category.icon ? (
                    category.icon.endsWith('.svg') ? (
                      <Image
                        src={category.icon}
                        alt={category.name}
                        width={16}
                        height={16}
                        className={
                          selectedCategoryId === category.id
                            ? 'text-primary-600'
                            : 'text-gray-600'
                        }
                      />
                    ) : (
                      <img
                        src={category.icon}
                        alt={category.name}
                        className={`w-4 h-4 object-contain ${
                          selectedCategoryId === category.id
                            ? 'text-primary-600'
                            : 'text-gray-600'
                        }`}
                      />
                    )
                  ) : (
                    <Image
                      src={`/icons/${category.name
                        .toLowerCase()
                        .replace(/\s+/g, '-')}.svg`}
                      alt={category.name}
                      width={16}
                      height={16}
                      className={
                        selectedCategoryId === category.id
                          ? 'text-primary-600'
                          : 'text-gray-600'
                      }
                    />
                  )}
                </div>
                <span
                  className={`text-sm font-medium ${
                    selectedCategoryId === category.id
                      ? 'text-primary-600'
                      : 'text-gray-700'
                  }`}
                >
                  {category.name}
                </span>
              </div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className={`transition-transform ${
                  expandedCategories.includes(category.id) ? 'rotate-180' : ''
                }`}
              >
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </button>

            {expandedCategories.includes(category.id) && (
              <div className="pl-10 pr-2 mt-1 space-y-1">
                {category.subCategories.map((subCategory) => (
                  <button
                    key={subCategory.id}
                    className={`flex items-center w-full py-2 px-2 rounded-md text-sm ${
                      selectedSubCategoryId === subCategory.id
                        ? 'bg-primary-50 text-primary-600 font-medium'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                    onClick={() =>
                      onSelectSubCategory(category.id, subCategory.id)
                    }
                  >
                    {subCategory.icon ? (
                      subCategory.icon.endsWith('.svg') ? (
                        <Image
                          src={subCategory.icon}
                          alt={subCategory.name}
                          width={14}
                          height={14}
                          className="mr-2"
                        />
                      ) : (
                        <img
                          src={subCategory.icon}
                          alt={subCategory.name}
                          className="w-3.5 h-3.5 object-contain mr-2"
                        />
                      )
                    ) : (
                      <Image
                        src={`/icons/${subCategory.name
                          .toLowerCase()
                          .replace(/\s+/g, '-')}.svg`}
                        alt={subCategory.name}
                        width={14}
                        height={14}
                        className="mr-2"
                      />
                    )}
                    <span className="text-gray-900">{subCategory.name}</span>
                    <span className="ml-auto text-xs text-gray-400">
                      {subCategory.serviceCount}
                    </span>
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default CategorySidebar;
