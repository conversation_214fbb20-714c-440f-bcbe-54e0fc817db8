'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import SubCategoryModal from './SubCategoryModal';
import { categoryService } from '@/lib/api/categoryService';
import { serviceService } from '@/lib/api/serviceService';

interface SubCategory {
  id: string;
  name: string;
  icon: string | null;
  serviceCount: number;
}

interface Category {
  id: string;
  name: string;
  icon: string | null;
  subCategories: SubCategory[];
}

// Minimal fallback categories in case API fails completely
const fallbackCategories: Category[] = [
  {
    id: 'cleaning',
    name: 'Cleaning',
    icon: '/icons/cleaning.svg',
    subCategories: [
      {
        id: 'bathroom-cleaning',
        name: 'Bathroom Cleaning',
        icon: '/icons/bathroom.svg',
        serviceCount: 5,
      },
    ],
  },
];

const CategorySection: React.FC<{
  onSelectSubCategory: (categoryId: string, subCategoryId: string) => void;
}> = ({ onSelectSubCategory }) => {
  const [categories, setCategories] = useState<Category[]>(fallbackCategories);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(
    null
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        // Fetch categories from the backend
        const categoriesData =
          await categoryService.getCategoriesWithSubcategories();

        if (categoriesData.length === 0) {
          setCategories(fallbackCategories);
          return;
        }

        // Transform the data to match our component's expected format
        const transformedCategories: Category[] = await Promise.all(
          categoriesData.map(async (category) => {
            // For each subcategory, fetch the service count
            const subCategories = await Promise.all(
              (category.subcategories || []).map(async (subcategory) => {
                // Fetch services for this subcategory to get the count
                const services = await serviceService.getSubcategoryServices(
                  subcategory.id
                );

                // Process the subcategory icon
                const iconUrl = subcategory.icon
                  ? subcategory.icon.startsWith('http')
                    ? subcategory.icon
                    : `http://localhost:8000${subcategory.icon}`
                  : `/icons/${subcategory.name
                      .toLowerCase()
                      .replace(/\s+/g, '-')}.svg`;

                return {
                  id: subcategory.id,
                  name: subcategory.name,
                  icon: iconUrl,
                  serviceCount: services.length,
                };
              })
            );

            // Process the category icon
            const iconUrl = category.icon
              ? category.icon.startsWith('http')
                ? category.icon
                : `http://localhost:8000${category.icon}`
              : `/icons/${category.name
                  .toLowerCase()
                  .replace(/\s+/g, '-')}.svg`;

            return {
              id: category.id,
              name: category.name,
              icon: iconUrl,
              subCategories,
            };
          })
        );

        setCategories(
          transformedCategories.length > 0
            ? transformedCategories
            : fallbackCategories
        );
        setError(null);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories. Using fallback data.');
        setCategories(fallbackCategories);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const handleCategoryClick = (category: Category) => {
    setSelectedCategory(category);
    setIsModalOpen(true);
  };

  const handleSubCategorySelect = (subCategoryId: string) => {
    if (selectedCategory) {
      onSelectSubCategory(selectedCategory.id, subCategoryId);
      setIsModalOpen(false);
    }
  };

  return (
    <div className="mb-8">
      <h2 className="text-2xl font-bold mb-4 text-gray-900">
        Select a service
      </h2>

      {loading ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div
              key={i}
              className="bg-white rounded-lg shadow-md p-4 flex flex-col items-center animate-pulse"
            >
              <div className="w-12 h-12 bg-gray-200 rounded-full mb-3"></div>
              <div className="h-4 bg-gray-200 rounded w-20 mb-1"></div>
            </div>
          ))}
        </div>
      ) : error ? (
        <div className="bg-white rounded-lg shadow-md p-4 text-center">
          <p className="text-red-500 mb-2">{error}</p>
          <p className="text-gray-500 text-sm">Using fallback data instead.</p>
        </div>
      ) : (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {categories.map((category) => (
            <motion.div
              key={category.id}
              className="bg-white rounded-lg shadow-md p-4 flex flex-col items-center cursor-pointer hover:shadow-lg transition-shadow"
              whileHover={{ y: -5 }}
              onClick={() => handleCategoryClick(category)}
            >
              <div className="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center mb-3">
                {category.icon ? (
                  category.icon.endsWith('.svg') ? (
                    <Image
                      src={category.icon}
                      alt={category.name}
                      width={24}
                      height={24}
                      className="text-primary-600"
                    />
                  ) : (
                    <img
                      src={category.icon}
                      alt={category.name}
                      className="w-6 h-6 object-contain"
                    />
                  )
                ) : (
                  <Image
                    src="/images/placeholder-icon.svg"
                    alt={category.name}
                    width={24}
                    height={24}
                    className="text-primary-600"
                  />
                )}
              </div>
              <span className="text-sm font-medium text-center text-gray-900">
                {category.name}
              </span>
            </motion.div>
          ))}
        </div>
      )}

      <AnimatePresence>
        {isModalOpen && selectedCategory && (
          <SubCategoryModal
            category={selectedCategory}
            onClose={() => setIsModalOpen(false)}
            onSelectSubCategory={handleSubCategorySelect}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default CategorySection;
