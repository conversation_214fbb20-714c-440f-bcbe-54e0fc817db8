'use client';

import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';

interface SubCategory {
  id: string;
  name: string;
  icon: string;
  serviceCount: number;
}

interface Category {
  id: string;
  name: string;
  icon: string;
  subCategories: SubCategory[];
}

interface SubCategoryModalProps {
  category: Category;
  onClose: () => void;
  onSelectSubCategory: (subCategoryId: string) => void;
}

const SubCategoryModal: React.FC<SubCategoryModalProps> = ({
  category,
  onClose,
  onSelectSubCategory,
}) => {
  return (
    <motion.div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="bg-white rounded-xl w-full max-w-md overflow-hidden"
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-4 border-b border-gray-200 flex items-center">
          <div className="w-10 h-10 bg-primary-50 rounded-full flex items-center justify-center mr-3">
            <Image
              src={category.icon}
              alt={category.name}
              width={20}
              height={20}
              className="text-primary-600"
            />
          </div>
          <h3 className="text-lg font-semibold flex-1 text-gray-900">
            {category.name}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <div className="p-4 max-h-[60vh] overflow-y-auto">
          <div className="grid grid-cols-2 gap-4">
            {category.subCategories.map((subCategory) => (
              <motion.div
                key={subCategory.id}
                className="bg-white border border-gray-200 rounded-lg p-3 flex flex-col items-center cursor-pointer hover:border-primary-500 hover:shadow-md transition-all"
                whileHover={{ y: -2 }}
                onClick={() => onSelectSubCategory(subCategory.id)}
              >
                <div className="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center mb-2">
                  <Image
                    src={subCategory.icon}
                    alt={subCategory.name}
                    width={24}
                    height={24}
                    className="text-primary-600"
                  />
                </div>
                <span className="text-sm font-medium text-center mb-1 text-gray-900">
                  {subCategory.name}
                </span>
                <span className="text-xs text-gray-500">
                  {subCategory.serviceCount} services
                </span>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default SubCategoryModal;
