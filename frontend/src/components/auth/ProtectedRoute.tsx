'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  userTypes?: string[];
}

/**
 * A wrapper component that protects routes from unauthorized access
 * @param children The components to render if authorized
 * @param userTypes Optional array of user types allowed to access the route
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children,
  userTypes = ['customer', 'vendor', 'admin']
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const { isLoggedIn, userType, loading } = useAuth();
  const [authorized, setAuthorized] = useState(false);

  useEffect(() => {
    // Check if user is logged in and has the right user type
    if (!loading) {
      if (!isLoggedIn) {
        // Redirect to login with the current path as redirect target
        router.push(`/auth/login?redirect=${encodeURIComponent(pathname)}`);
      } else if (userTypes.length > 0 && userType && !userTypes.includes(userType)) {
        // User is logged in but doesn't have the right user type
        router.push('/unauthorized');
      } else {
        // User is authorized
        setAuthorized(true);
      }
    }
  }, [isLoggedIn, userType, loading, router, pathname, userTypes]);

  // Show loading state while checking authorization
  if (loading || !authorized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-10 w-10 border-4 border-indigo-500 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  // Render children if authorized
  return <>{children}</>;
};

export default ProtectedRoute;
