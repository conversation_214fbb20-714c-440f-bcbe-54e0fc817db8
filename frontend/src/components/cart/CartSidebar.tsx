'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useCart } from '@/lib/contexts/CartContext';
import { useRouter } from 'next/navigation';

interface CartSidebarProps {
  onCheckout?: () => void;
}

const CartSidebar: React.FC<CartSidebarProps> = ({ onCheckout }) => {
  const { cart, loading, error, updateQuantity } = useCart();
  const router = useRouter();

  const handleCheckout = () => {
    if (onCheckout) {
      onCheckout();
    } else {
      router.push('/checkout');
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-4 sticky top-24">
        <h2 className="text-xl font-bold mb-4 text-gray-900">Cart</h2>
        <div className="text-center py-8 text-gray-500">
          <div className="animate-pulse flex flex-col items-center">
            <div className="rounded-full bg-gray-200 h-12 w-12 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-24 mb-2.5"></div>
            <div className="h-4 bg-gray-200 rounded w-32"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-4 sticky top-24">
        <h2 className="text-xl font-bold mb-4 text-gray-900">Cart</h2>
        <div className="text-center py-8 text-red-500">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (!cart || cart.items.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-4 sticky top-24">
        <h2 className="text-xl font-bold mb-4 text-gray-900">Cart</h2>
        <div className="text-center py-8 text-gray-500">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="48"
            height="48"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mx-auto mb-4 text-gray-300"
          >
            <circle cx="9" cy="21" r="1"></circle>
            <circle cx="20" cy="21" r="1"></circle>
            <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
          </svg>
          <p>Your cart is empty</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-4 sticky top-24">
      <h2 className="text-xl font-bold mb-4 text-gray-900">Cart</h2>

      <div className="space-y-4 mb-6">
        {cart.items.map((item) => (
          <div key={item.id} className="flex border-b border-gray-100 pb-4">
            <div className="flex-1">
              <h3 className="font-medium text-gray-900">
                {item.service.title || item.service.name}
              </h3>
              {item.service.bathrooms && (
                <p className="text-sm text-gray-500">
                  ({item.service.bathrooms} bathrooms)
                </p>
              )}
              <div className="flex items-center mt-2">
                <button
                  onClick={() => updateQuantity(item.id, item.quantity - 1)}
                  className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-l-md bg-gray-50"
                  disabled={item.quantity <= 1}
                >
                  -
                </button>
                <span className="w-8 h-8 flex items-center justify-center border-t border-b border-gray-300 bg-white">
                  {item.quantity}
                </span>
                <button
                  onClick={() => updateQuantity(item.id, item.quantity + 1)}
                  className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-r-md bg-gray-50"
                >
                  +
                </button>
              </div>
            </div>
            <div className="text-right">
              <p className="font-semibold">
                ₹
                {(item.service.discounted_price ||
                  item.service.base_price ||
                  item.service.price) * item.quantity}
              </p>
              {(item.service.discounted_price ||
                (item.service.price && item.service.discountedPrice)) && (
                <p className="text-sm text-gray-500 line-through">
                  ₹
                  {(item.service.base_price || item.service.price) *
                    item.quantity}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="space-y-2 text-sm border-b border-gray-100 pb-4 mb-4">
        <div className="flex justify-between">
          <span className="text-gray-600">Subtotal</span>
          <span>₹{cart.subtotal}</span>
        </div>
        {cart.discount > 0 && (
          <div className="flex justify-between text-green-600">
            <span>Discount</span>
            <span>-₹{cart.discount}</span>
          </div>
        )}
      </div>

      <div className="flex justify-between font-bold mb-6">
        <span>Total</span>
        <span>₹{cart.total}</span>
      </div>

      {cart.discount > 0 && (
        <div className="bg-green-50 p-2 rounded-md text-center text-green-700 text-sm font-medium mb-4">
          Congratulations! ₹{cart.discount} saved so far!
        </div>
      )}

      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className="w-full bg-primary-600 text-white py-3 rounded-md font-medium"
        onClick={handleCheckout}
      >
        Proceed to Checkout
      </motion.button>

      <div className="mt-4 bg-gray-50 p-3 rounded-md">
        <div className="flex items-center mb-2">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-primary-600 mr-2"
          >
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
          </svg>
          <h3 className="font-medium text-gray-900">DODO Promise</h3>
        </div>
        <ul className="text-sm text-gray-600 space-y-1">
          <li className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500 mr-2"
            >
              <polyline points="20 6 9 17 4 12"></polyline>
            </svg>
            Verified Professionals
          </li>
          <li className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500 mr-2"
            >
              <polyline points="20 6 9 17 4 12"></polyline>
            </svg>
            Safe Chemicals
          </li>
          <li className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-green-500 mr-2"
            >
              <polyline points="20 6 9 17 4 12"></polyline>
            </svg>
            Superior Service Quality
          </li>
        </ul>
      </div>
    </div>
  );
};

export default CartSidebar;
