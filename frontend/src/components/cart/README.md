# Cart Components

This directory contains components for displaying and managing the shopping cart in the DODO BOOKER application.

## Components

### CartSidebar

A sidebar component that displays the items in the cart, allows users to update quantities, and shows the total price. It also includes a "Proceed to Checkout" button.

## Cart Data Structure

The CartItem interface defines the structure of an item in the cart:

```typescript
interface CartItem {
  service: Service;
  quantity: number;
}
```

Where `Service` is the same interface used in the service components.

## Features

- Display cart items with their details
- Allow users to update quantities
- Show subtotal, discount, and total
- Display a congratulatory message when the user has saved money through discounts
- Show DODO BOOKER promises (verified professionals, safe chemicals, etc.)
- Empty cart state with a message and icon

## Design Principles

- The cart sidebar is sticky, so it stays visible as the user scrolls
- The UI follows a clean, modern design similar to Urban Company
- The layout is responsive and works well on mobile and desktop

## Usage

```tsx
import CartSidebar from '@/components/cart/CartSidebar';

// Example cart data
const cartItems = [
  {
    service: {
      id: 'service-1',
      title: 'Bathroom Cleaning',
      description: 'Professional bathroom cleaning service',
      price: 999,
      discountedPrice: 899,
      rating: 4.8,
      reviewCount: 120,
      duration: '2 hrs',
      image: '/images/services/bathroom-cleaning.jpg',
      bathrooms: 2,
    },
    quantity: 1,
  },
  // More cart items...
];

// Using CartSidebar component
<CartSidebar
  cartItems={cartItems}
  onUpdateQuantity={(serviceId, quantity) => {
    // Handle updating quantity
  }}
  onCheckout={() => {
    // Handle checkout
  }}
/>
```
