'use client';

import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { isValidOTP } from '@/lib/utils/index';

interface OTPFormProps {
  phoneNumber: string;
  onVerify: (otp: string) => void;
  onResend: () => void;
  isLoading: boolean;
  error?: string;
  successMessage?: string;
  expiresAt?: string;
}

const OTPForm: React.FC<OTPFormProps> = ({
  phoneNumber,
  onVerify,
  onResend,
  isLoading,
  error,
  successMessage,
  expiresAt,
}) => {
  const [otp, setOtp] = useState<string>('');
  const [otpError, setOtpError] = useState<string>('');
  const [timeLeft, setTimeLeft] = useState<number>(0);

  // Calculate time left for OTP expiry
  useEffect(() => {
    if (expiresAt) {
      const expiryTime = new Date(expiresAt).getTime();
      const now = new Date().getTime();
      const initialTimeLeft = Math.max(
        0,
        Math.floor((expiryTime - now) / 1000)
      );

      setTimeLeft(initialTimeLeft);

      const timer = setInterval(() => {
        setTimeLeft((prevTime) => {
          if (prevTime <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [expiresAt]);

  // Add a redirect effect when success message is shown
  useEffect(() => {
    if (successMessage) {
      console.log('Success message detected in OTPForm, setting up redirect');
      
      // Force direct navigation to the appropriate page
      const timer = setTimeout(() => {
        // Check for redirect destination in sessionStorage
        const redirectDestination = sessionStorage.getItem('redirect_destination');
        
        // Determine where to redirect
        const redirectUrl = redirectDestination || '/customer';
        console.log('OTPForm redirecting to:', redirectUrl);
        
        try {
          // Set a flag in sessionStorage to indicate we're in the middle of a redirect
          sessionStorage.setItem('redirecting_after_auth', 'true');
          
          // Use direct navigation
          window.location.assign(redirectUrl);
        } catch (error) {
          console.error('OTPForm navigation error:', error);
          // Fallback
          document.location.href = redirectUrl;
        }
      }, 1200);
      
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('OTP Form submitted with:', otp);

    if (!otp) {
      setOtpError('OTP is required');
      return;
    }

    if (!isValidOTP(otp)) {
      setOtpError('OTP must be 6 digits');
      return;
    }

    setOtpError('');
    console.log('Calling onVerify with OTP:', otp);
    onVerify(otp);
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Verify OTP</h2>
        <p className="mt-2 text-sm text-gray-600">
          We've sent a 6-digit OTP to {phoneNumber}
        </p>
      </div>

      {successMessage ? (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
          <p className="text-green-700 text-center font-medium">{successMessage}</p>
          <div className="mt-2 flex justify-center">
            <div className="animate-spin h-5 w-5 border-2 border-green-500 rounded-full border-t-transparent"></div>
          </div>
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600">
              If you are not redirected automatically, click the button below:
            </p>
            <button
              onClick={() => {
                console.log('Manual redirect button clicked');
                try {
                  // Get redirect destination from sessionStorage
                  const redirectDestination = sessionStorage.getItem('redirect_destination');
                  const redirectUrl = redirectDestination || '/customer';
                  
                  // Set a flag in sessionStorage to indicate we're in the middle of a redirect
                  sessionStorage.setItem('redirecting_after_auth', 'true');
                  window.location.assign(redirectUrl);
                } catch (error) {
                  console.error('Manual navigation error:', error);
                  // Last resort
                  window.open('/customer', '_self');
                }
              }}
              className="mt-2 text-indigo-600 hover:text-indigo-800 font-medium"
            >
              Click here to continue
            </button>
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            label="Enter 6-digit OTP"
            type="text"
            value={otp}
            onChange={(e) =>
              setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))
            }
            placeholder="Enter 6-digit OTP"
            maxLength={6}
            error={otpError || error}
            disabled={isLoading}
            className="text-center text-lg tracking-widest"
          />

          <Button
            type="button"
            variant="brand"
            size="full"
            isLoading={isLoading}
            disabled={isLoading || !otp || !isValidOTP(otp)}
            onClick={() => {
              if (otp && isValidOTP(otp)) {
                console.log('Verifying OTP:', otp);
                onVerify(otp);
              } else if (!otp) {
                setOtpError('OTP is required');
              } else {
                setOtpError('OTP must be 6 digits');
              }
            }}
          >
            Verify OTP
          </Button>

          <div className="flex items-center justify-between mt-4">
            <p className="text-sm text-gray-600">
              {timeLeft > 0 ? (
                <span>OTP expires in {formatTime(timeLeft)}</span>
              ) : (
                <span>OTP expired</span>
              )}
            </p>

            <Button
              type="button"
              variant="ghost"
              onClick={onResend}
              disabled={isLoading || timeLeft > 0}
              className="text-indigo-600 hover:text-indigo-800"
            >
              Resend OTP
            </Button>
          </div>
        </form>
      )}
    </div>
  );
};

export default OTPForm;
