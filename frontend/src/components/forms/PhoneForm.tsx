'use client';

import React, { useState } from 'react';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { isValidPhoneNumber } from '@/lib/utils/index';

interface PhoneFormProps {
  onSubmit: (phoneNumber: string) => void;
  isLoading: boolean;
  error?: string;
}

const PhoneForm: React.FC<PhoneFormProps> = ({
  onSubmit,
  isLoading,
  error,
}) => {
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [phoneError, setPhoneError] = useState<string>('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!phoneNumber) {
      setPhoneError('Phone number is required');
      return;
    }

    if (!isValidPhoneNumber(phoneNumber)) {
      setPhoneError('Please enter a valid 10-digit phone number');
      return;
    }

    setPhoneError('');
    onSubmit(phoneNumber);
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Login / Sign Up</h2>
        <p className="mt-2 text-sm text-gray-600">
          Enter your phone number to continue
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          label="Phone Number"
          type="tel"
          value={phoneNumber}
          onChange={(e) =>
            setPhoneNumber(e.target.value.replace(/\D/g, '').slice(0, 10))
          }
          placeholder="Enter 10-digit phone number"
          maxLength={10}
          error={phoneError || error}
          disabled={isLoading}
        />

        <Button
          type="submit"
          variant="brand"
          size="full"
          isLoading={isLoading}
          disabled={isLoading || !phoneNumber}
        >
          Continue
        </Button>

        <p className="text-xs text-center text-gray-500 mt-4">
          By continuing, you agree to our Terms of Service and Privacy Policy
        </p>
      </form>
    </div>
  );
};

export default PhoneForm;
