'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { isAuthenticated } from '@/lib/auth';
import { Button } from '@/components/ui/Button';

interface CheckoutAuthGuardProps {
  children: React.ReactNode;
  returnUrl?: string;
}

/**
 * Component that guards checkout process, ensuring user is authenticated
 * If not authenticated, shows a login prompt
 */
const CheckoutAuthGuard: React.FC<CheckoutAuthGuardProps> = ({
  children,
  returnUrl = '/customer/checkout',
}) => {
  const router = useRouter();
  const [isAuthed, setIsAuthed] = useState<boolean | null>(null);

  // Check authentication status on mount and when localStorage changes
  useEffect(() => {
    const checkAuth = () => {
      const authed = isAuthenticated();
      setIsAuthed(authed);
    };

    // Initial check
    checkAuth();

    // Listen for storage events (in case user logs in in another tab)
    const handleStorageChange = () => {
      checkAuth();
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Handle login redirect
  const handleLoginRedirect = () => {
    // Encode the return URL to handle special characters
    const encodedReturnUrl = encodeURIComponent(returnUrl);
    
    // Redirect to login with return URL as parameter
    router.push(`/auth/login?redirect=${encodedReturnUrl}`);
  };

  // Show loading state while checking auth
  if (isAuthed === null) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin h-8 w-8 border-4 border-indigo-500 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  // If authenticated, show children (checkout content)
  if (isAuthed) {
    return <>{children}</>;
  }

  // If not authenticated, show login prompt
  return (
    <div className="bg-white rounded-lg shadow-md p-6 max-w-md mx-auto my-8">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Login Required</h2>
        <p className="text-gray-600 mb-6">
          Please log in to continue with your checkout process. Your cart items will be saved.
        </p>
        <Button 
          variant="brand" 
          size="lg" 
          onClick={handleLoginRedirect}
        >
          Login to Continue
        </Button>
      </div>
    </div>
  );
};

export default CheckoutAuthGuard;
