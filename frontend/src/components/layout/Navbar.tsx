'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { useCart } from '@/lib/cart/CartContext';
import { Button } from '@/components/ui/Button';
import LocationModal from '@/components/location/LocationModal';
import SearchBar from '@/components/search/SearchBar';

interface NavbarProps {
  showSearch?: boolean;
  showLocationSelector?: boolean;
}

const Navbar: React.FC<NavbarProps> = ({
  showSearch = true,
  showLocationSelector = true,
}) => {
  const { isLoggedIn, user, logout } = useAuth();
  const { totalItems } = useCart();
  const router = useRouter();
  const pathname = usePathname();
  const [isClient, setIsClient] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState('Hyderabad');
  const [selectedFullAddress, setSelectedFullAddress] = useState(
    'Hyderabad, Telangana, India'
  );
  const userMenuRef = useRef<HTMLDivElement>(null);

  // Set isClient to true on mount
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        userMenuRef.current &&
        !userMenuRef.current.contains(event.target as Node)
      ) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle location selection
  const handleLocationSelect = (location: string, fullAddress?: string) => {
    setSelectedLocation(location);
    if (fullAddress) {
      setSelectedFullAddress(fullAddress);
    } else {
      setSelectedFullAddress(location);
    }
  };

  // Handle logout
  const handleLogout = () => {
    logout();
    router.push('/');
  };

  return (
    <header className="bg-white text-gray-800 sticky top-0 z-50 shadow-md">
      <div className="container mx-auto px-4">
        {/* Main navbar */}
        <div className="flex items-center justify-between py-3">
          {/* Logo */}
          <Link
            href="/customer"
            className="text-2xl font-bold flex items-center text-primary-600"
          >
            <span className="mr-2">🏠</span>
            DODO BOOKER
          </Link>

          {/* Search bar - only on larger screens */}
          {showSearch && (
            <div className="hidden md:block flex-grow mx-8 max-w-2xl relative">
              <SearchBar
                selectedLocation={selectedLocation}
                onLocationClick={
                  showLocationSelector
                    ? () => setShowLocationModal(true)
                    : undefined
                }
              />
            </div>
          )}

          {/* Right side navigation */}
          {isClient && (
            <div className="flex items-center space-x-4">
              {/* Cart icon */}
              <Link href="/customer/cart" className="relative p-2">
                <span className="text-2xl">🛒</span>
                {totalItems > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {totalItems}
                  </span>
                )}
              </Link>

              {/* User menu */}
              {isLoggedIn ? (
                <div className="relative" ref={userMenuRef}>
                  <button
                    className="flex items-center space-x-2"
                    onClick={() => setShowUserMenu(!showUserMenu)}
                  >
                    <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white">
                      <span className="text-sm font-bold">
                        {user?.phone_number?.substring(0, 2) || 'U'}
                      </span>
                    </div>
                    <span className="hidden md:inline">
                      {user?.phone_number || 'User'}
                    </span>
                  </button>

                  {/* User dropdown menu */}
                  {showUserMenu && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                      <div className="py-1 text-gray-800">
                        <Link
                          href="/customer/profile"
                          className="block px-4 py-2 hover:bg-gray-100"
                          onClick={() => setShowUserMenu(false)}
                        >
                          Profile
                        </Link>
                        <Link
                          href="/customer/orders"
                          className="block px-4 py-2 hover:bg-gray-100"
                          onClick={() => setShowUserMenu(false)}
                        >
                          My Orders
                        </Link>
                        <button
                          className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-red-600"
                          onClick={handleLogout}
                        >
                          Logout
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <Link href="/auth/login">
                  <Button
                    variant="white"
                    size="sm"
                    className="text-primary-600 border border-primary-500"
                  >
                    Login
                  </Button>
                </Link>
              )}
            </div>
          )}
        </div>

        {/* Mobile search bar - only visible on small screens */}
        {showSearch && (
          <div className="md:hidden pb-3">
            <SearchBar
              selectedLocation={selectedLocation}
              onLocationClick={
                showLocationSelector
                  ? () => setShowLocationModal(true)
                  : undefined
              }
              variant="mobile"
            />
          </div>
        )}
      </div>

      {/* Location Modal */}
      <LocationModal
        isOpen={showLocationModal}
        onClose={() => setShowLocationModal(false)}
        onSelectLocation={handleLocationSelect}
        currentLocation={selectedLocation}
      />
    </header>
  );
};

export default Navbar;
