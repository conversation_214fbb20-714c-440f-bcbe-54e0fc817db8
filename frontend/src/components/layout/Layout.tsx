'use client';

import React from 'react';
import Navbar from './Navbar';
import Footer from './Footer';
import { CartProvider } from '@/lib/contexts/CartContext';

interface LayoutProps {
  children: React.ReactNode;
  showNavbar?: boolean;
  showFooter?: boolean;
  showSearch?: boolean;
  showLocationSelector?: boolean;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  showNavbar = true,
  showFooter = true,
  showSearch = true,
  showLocationSelector = true,
}) => {
  return (
    <CartProvider>
      <div className="flex flex-col min-h-screen bg-gray-50">
        {showNavbar && (
          <Navbar
            showSearch={showSearch}
            showLocationSelector={showLocationSelector}
          />
        )}
        <main className="flex-grow">{children}</main>
        {showFooter && <Footer />}
      </div>
    </CartProvider>
  );
};

export default Layout;
