.locationModal {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.modalContent {
  background-color: white;
  border-radius: 1rem;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  overflow: hidden;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

@media (min-width: 640px) {
  .locationModal {
    padding: 1rem;
  }

  .modalContent {
    height: auto;
    max-width: 28rem;
    max-height: 90vh;
    border-radius: 0.5rem;
  }
}

.header {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #fff;
}

.searchContainer {
  display: flex;
  align-items: center;
}

.backButton {
  margin-right: 0.75rem;
  color: #6b7280;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
}

.searchInputContainer {
  position: relative;
  flex-grow: 1;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: none;
  outline: none;
  color: #1f2937;
  font-size: 1rem;
  background-color: #f3f4f6;
  border-radius: 0.5rem;
}

.searchIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
}

.currentLocationButton {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  color: #4f46e5;
  font-weight: 500;
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.locationIcon {
  width: 2.25rem;
  height: 2.25rem;
  background-color: #ede9fe;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: #4f46e5;
  flex-shrink: 0;
}

.locationsList {
  max-height: 60vh;
  overflow-y: auto;
}

.sectionHeader {
  padding: 0.75rem 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
  background-color: #f9fafb;
  letter-spacing: 0.05em;
}

.locationItem {
  width: 100%;
  text-align: left;
  padding: 1rem;
  display: flex;
  align-items: flex-start;
  background: none;
  border: none;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
}

.locationItem:hover {
  background-color: #f9fafb;
}

.locationItemIcon {
  color: #9ca3af;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 1.5rem;
}

.locationDetails {
  display: flex;
  flex-direction: column;
}

.locationName {
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: #111827;
}

.locationAddress {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.25;
}

.footer {
  padding: 0.75rem;
  border-top: 1px solid #e5e7eb;
  text-align: center;
  background-color: #fff;
  position: sticky;
  bottom: 0;
}

.poweredBy {
  font-size: 0.75rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
}

.googleLogo {
  margin-left: 0.25rem;
  color: #374151;
  font-weight: 500;
}
