'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import styles from './LocationModal.module.css';

interface LocationItem {
  name: string;
  fullAddress: string;
}

interface LocationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectLocation: (location: string, fullAddress?: string) => void;
  currentLocation?: string;
}

const LocationModal: React.FC<LocationModalProps> = ({
  isOpen,
  onClose,
  onSelectLocation,
  currentLocation = '',
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [recentLocations, setRecentLocations] = useState<LocationItem[]>([]);
  const modalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  // Mock popular locations
  const popularLocations = [
    { name: 'Hyderabad', fullAddress: 'Hyderabad, Telangana, India' },
    { name: 'Bangalore', fullAddress: 'Bangalore, Karnataka, India' },
    { name: 'Mumbai', fullAddress: 'Mumbai, Maharashtra, India' },
    { name: 'Delhi', fullAddress: 'Delhi, India' },
    { name: 'Chennai', fullAddress: 'Chennai, Tamil Nadu, India' },
    { name: 'Kolkata', fullAddress: 'Kolkata, West Bengal, India' },
    { name: 'Pune', fullAddress: 'Pune, Maharashtra, India' },
  ];

  // Mock search results based on query
  const getSearchResults = (query: string) => {
    if (!query.trim()) return [];

    return popularLocations
      .filter(
        (location) =>
          location.name.toLowerCase().includes(query.toLowerCase()) ||
          location.fullAddress.toLowerCase().includes(query.toLowerCase())
      )
      .slice(0, 5);
  };

  const searchResults = getSearchResults(searchQuery);

  // Load recent locations from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedLocations = localStorage.getItem('recentLocations');
        if (savedLocations) {
          const parsedLocations = JSON.parse(savedLocations);

          // Check if the saved locations are in the correct format
          if (Array.isArray(parsedLocations) && parsedLocations.length > 0) {
            // If the first item doesn't have name and fullAddress properties,
            // convert the old format to the new format
            if (typeof parsedLocations[0] === 'string') {
              const convertedLocations = parsedLocations.map((loc: string) => ({
                name: loc,
                fullAddress: loc,
              }));
              setRecentLocations(convertedLocations);
            } else {
              setRecentLocations(parsedLocations);
            }
          }
        }
      } catch (error) {
        console.error('Error loading recent locations:', error);
        // Clear corrupted localStorage data
        localStorage.removeItem('recentLocations');
      }
    }
  }, []);

  // Focus input when modal opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Handle click outside to close modal
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Handle location selection
  const handleSelectLocation = (location: string, fullAddress?: string) => {
    onSelectLocation(location, fullAddress);

    // Save to recent locations
    if (typeof window !== 'undefined') {
      const newLocation = {
        name: location,
        fullAddress: fullAddress || location,
      };

      const updatedLocations = [
        newLocation,
        ...recentLocations.filter((loc) => loc.name !== location).slice(0, 4),
      ];

      localStorage.setItem('recentLocations', JSON.stringify(updatedLocations));
      setRecentLocations(updatedLocations);
    }

    onClose();
  };

  // Handle use current location
  const handleUseCurrentLocation = () => {
    // In a real app, this would use the Geolocation API
    // For this demo, we'll just use a mock location
    handleSelectLocation('Current Location', 'Your current location');
  };

  if (!isOpen) return null;

  return (
    <div className={styles.locationModal}>
      <div ref={modalRef} className={styles.modalContent}>
        {/* Header with search input */}
        <div className={styles.header}>
          <div className={styles.searchContainer}>
            <button onClick={onClose} className={styles.backButton}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M19 12H5M12 19l-7-7 7-7" />
              </svg>
            </button>
            <div className={styles.searchInputContainer}>
              <input
                ref={inputRef}
                type="text"
                placeholder="Search for your location/society/apartment"
                className={styles.searchInput}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <span className={styles.searchIcon}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="11" cy="11" r="8"></circle>
                  <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
              </span>
            </div>
          </div>
        </div>

        {/* Current location button */}
        <button
          onClick={handleUseCurrentLocation}
          className={styles.currentLocationButton}
        >
          <span className={styles.locationIcon}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M12 22s-8-4.5-8-11.8A8 8 0 0 1 12 2a8 8 0 0 1 8 8.2c0 7.3-8 11.8-8 11.8z" />
              <circle cx="12" cy="10" r="3" />
            </svg>
          </span>
          Use current location
        </button>

        {/* Search results or recent locations */}
        <div className={styles.locationsList}>
          {searchQuery ? (
            // Search results
            <div>
              {searchResults.length > 0 ? (
                <div>
                  {searchResults.map((location, index) => (
                    <button
                      key={index}
                      className={styles.locationItem}
                      onClick={() =>
                        handleSelectLocation(
                          location.name,
                          location.fullAddress
                        )
                      }
                    >
                      <span className={styles.locationItemIcon}>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M12 22s-8-4.5-8-11.8A8 8 0 0 1 12 2a8 8 0 0 1 8 8.2c0 7.3-8 11.8-8 11.8z" />
                          <circle cx="12" cy="10" r="3" />
                        </svg>
                      </span>
                      <div className={styles.locationDetails}>
                        <div className={styles.locationName}>
                          {location.name}
                        </div>
                        <div className={styles.locationAddress}>
                          {location.fullAddress}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              ) : (
                <div className="p-4 text-center text-gray-500">
                  No locations found
                </div>
              )}
            </div>
          ) : (
            // Recent locations
            <div>
              {recentLocations.length > 0 && (
                <div>
                  <div className={styles.sectionHeader}>RECENTS</div>
                  {recentLocations.map((location, index) => (
                    <button
                      key={index}
                      className={styles.locationItem}
                      onClick={() =>
                        handleSelectLocation(
                          location.name,
                          location.fullAddress
                        )
                      }
                    >
                      <span className={styles.locationItemIcon}>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <circle cx="12" cy="12" r="10" />
                          <polyline points="12 6 12 12 16 14" />
                        </svg>
                      </span>
                      <div className={styles.locationDetails}>
                        <div className={styles.locationName}>
                          {location.name}
                        </div>
                        <div className={styles.locationAddress}>
                          {location.fullAddress}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}

              {/* Popular locations */}
              <div>
                <div className={styles.sectionHeader}>POPULAR LOCATIONS</div>
                {popularLocations.map((location, index) => (
                  <button
                    key={index}
                    className={styles.locationItem}
                    onClick={() =>
                      handleSelectLocation(location.name, location.fullAddress)
                    }
                  >
                    <span className={styles.locationItemIcon}>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M12 22s-8-4.5-8-11.8A8 8 0 0 1 12 2a8 8 0 0 1 8 8.2c0 7.3-8 11.8-8 11.8z" />
                        <circle cx="12" cy="10" r="3" />
                      </svg>
                    </span>
                    <div className={styles.locationDetails}>
                      <div className={styles.locationName}>{location.name}</div>
                      <div className={styles.locationAddress}>
                        {location.fullAddress}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Powered by Google */}
        <div className={styles.footer}>
          <div className={styles.poweredBy}>
            powered by
            <span className={styles.googleLogo}>Google</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LocationModal;
