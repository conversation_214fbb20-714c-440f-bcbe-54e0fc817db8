'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface SearchBarProps {
  placeholder?: string;
  selectedLocation: string;
  onLocationClick?: () => void;
  className?: string;
  variant?: 'default' | 'mobile';
}

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = "Search for 'Facial'",
  selectedLocation,
  onLocationClick,
  className = '',
  variant = 'default',
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<string[]>([]);
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Mock search results
  const mockServices = [
    'Home Cleaning',
    'Bathroom Cleaning',
    'Kitchen Cleaning',
    'AC Repair',
    'AC Service',
    'Refrigerator Repair',
    'Plumbing Services',
    'Pipe Fitting',
    'Water Heater Installation',
    'Electrical Repairs',
    'Fan Installation',
    'Light Fixture Installation',
    'House Painting',
    'Wall Painting',
    'Furniture Painting',
    'Pest Control',
    'Cockroach Control',
    'Termite Control',
    'Carpentry',
    'Furniture Assembly',
    'Door Repair',
    'Salon at Home',
    'Haircut',
    'Massage',
    'Facial',
    'Manicure',
    'Pedicure',
    'Waxing',
    'Makeup',
    'Hair Coloring',
  ];

  // Handle search input
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setSearchResults([]);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filteredResults = mockServices
      .filter((service) => service.toLowerCase().includes(query))
      .slice(0, 5); // Limit to 5 results

    setSearchResults(filteredResults);
  }, [searchQuery]);

  // Close search results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        resultsRef.current &&
        !resultsRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setSearchResults([]);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle search submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(
        `/customer/search?q=${encodeURIComponent(
          searchQuery
        )}&location=${encodeURIComponent(selectedLocation)}`
      );
      setSearchResults([]);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {variant === 'mobile' && (
        <div className="flex items-center mb-3">
          {onLocationClick && (
            <button
              onClick={onLocationClick}
              className="flex items-center text-gray-700 mr-2"
            >
              <span className="mr-1 text-gray-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M12 22s-8-4.5-8-11.8A8 8 0 0 1 12 2a8 8 0 0 1 8 8.2c0 7.3-8 11.8-8 11.8z" />
                  <circle cx="12" cy="10" r="3" />
                </svg>
              </span>
              <span className="truncate max-w-[200px] font-medium">
                {selectedLocation}
              </span>
              <span className="ml-1 text-gray-500 text-xs">▼</span>
            </button>
          )}
        </div>
      )}

      <form onSubmit={handleSearchSubmit} className="flex">
        {variant === 'default' && onLocationClick && (
          <button
            type="button"
            className="bg-white text-gray-700 px-3 py-2 rounded-l-md flex items-center border border-gray-300 border-r-0"
            onClick={onLocationClick}
          >
            <span className="mr-1 text-gray-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M12 22s-8-4.5-8-11.8A8 8 0 0 1 12 2a8 8 0 0 1 8 8.2c0 7.3-8 11.8-8 11.8z" />
                <circle cx="12" cy="10" r="3" />
              </svg>
            </span>
            <span className="truncate max-w-[140px] font-medium">
              {selectedLocation}
            </span>
            <span className="ml-1 text-gray-500 text-xs">▼</span>
          </button>
        )}

        <div className="relative flex-grow">
          <input
            ref={inputRef}
            type="text"
            placeholder={placeholder}
            className={`w-full py-2 pl-10 pr-4 text-gray-800 focus:outline-none border border-gray-300 ${
              variant === 'default' && onLocationClick
                ? 'rounded-r-md'
                : 'rounded-md'
            } bg-gray-50`}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </span>
        </div>

        {variant === 'mobile' ? (
          <button
            type="submit"
            className="bg-accent-500 text-white px-4 py-2 rounded-r-md font-medium"
          >
            Search
          </button>
        ) : null}
      </form>

      {/* Search results dropdown */}
      {searchResults.length > 0 && (
        <div
          ref={resultsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white rounded-md shadow-lg z-10"
        >
          <div className="py-1 text-gray-800">
            {searchResults.map((result) => (
              <Link
                key={result}
                href={`/customer/search?q=${encodeURIComponent(
                  result
                )}&location=${encodeURIComponent(selectedLocation)}`}
                className="block px-4 py-2 hover:bg-gray-100"
                onClick={() => setSearchResults([])}
              >
                {result}
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchBar;
