# Service Components

This directory contains components for displaying services in the DODO BOOKER application.

## Components

### ServiceCard

A card component that displays a service with its image, title, price, rating, and other details. It also includes an "Add" button to add the service to the cart and a "View details" link to view more information about the service.

### ServiceList

A grid component that displays a list of ServiceCard components. It handles the layout and spacing of the cards.

## Service Data Structure

The Service interface defines the structure of a service:

```typescript
interface Service {
  id: string;
  title: string;
  description: string;
  price: number;
  discountedPrice?: number;
  rating: number;
  reviewCount: number;
  duration: string;
  image: string;
  features?: string[];
  bathrooms?: number;
}
```

## Design Principles

- Services use images to showcase the actual service
- Each service card displays key information like price, rating, and duration
- The UI follows a clean, modern design similar to Urban Company
- The layout is responsive and works well on mobile and desktop

## Usage

```tsx
import ServiceCard from '@/components/service/ServiceCard';
import ServiceList from '@/components/service/ServiceList';

// Example service data
const services = [
  {
    id: 'service-1',
    title: 'Bathroom Cleaning',
    description: 'Professional bathroom cleaning service',
    price: 999,
    discountedPrice: 899,
    rating: 4.8,
    reviewCount: 120,
    duration: '2 hrs',
    image: '/images/services/bathroom-cleaning.jpg',
    bathrooms: 2,
  },
  // More services...
];

// Using ServiceList component
<ServiceList 
  services={services} 
  onAddToCart={(serviceId) => {
    // Handle adding service to cart
  }} 
/>

// Using ServiceCard component directly
<ServiceCard
  service={services[0]}
  onAddToCart={() => {
    // Handle adding service to cart
  }}
/>
```
