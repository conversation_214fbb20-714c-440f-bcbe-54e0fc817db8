'use client';

import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { useCart } from '@/lib/contexts/CartContext';
import { Service } from '@/lib/types/service';

interface VerticalServiceCardProps {
  service: Service;
}

const VerticalServiceCard: React.FC<VerticalServiceCardProps> = ({
  service,
}) => {
  const { addToCart } = useCart();

  const handleAddToCart = () => {
    addToCart(service, 1);
  };

  const discount =
    service.discount_percentage ||
    (service.discountedPrice && service.price
      ? Math.round(
          ((service.price - service.discountedPrice) / service.price) * 100
        )
      : 0);

  return (
    <motion.div
      className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100"
      whileHover={{ y: -5, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' }}
      transition={{ duration: 0.2 }}
    >
      <div className="flex flex-col h-full">
        {/* Top - Image */}
        <div className="relative h-48">
          <Image
            src={
              service.image ||
              (service.images && service.images.length > 0
                ? service.images[0].image
                : '/images/placeholder.jpg')
            }
            alt={service.title || service.name}
            width={400}
            height={200}
            className="w-full h-full object-cover"
          />
          {service.bathrooms && (
            <div className="absolute top-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
              {service.bathrooms} BATHROOMS
            </div>
          )}
        </div>

        {/* Bottom - Content */}
        <div className="p-4 flex-1 flex flex-col justify-between">
          <div>
            <h3 className="font-semibold text-lg mb-1 text-gray-900">
              {service.title || service.name}
            </h3>

            <div className="flex items-center mb-2">
              <div className="flex items-center bg-green-50 px-2 py-1 rounded">
                <span className="text-green-700 font-medium text-sm mr-1">
                  {service.rating || service.average_rating || 4.5}
                </span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  className="text-green-700"
                >
                  <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
                </svg>
              </div>
              <span className="text-gray-500 text-xs ml-2">
                ({service.reviewCount || service.total_ratings || 0} reviews)
              </span>
            </div>

            <div className="flex items-center mb-3">
              <div className="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-gray-400 mr-1"
                >
                  <circle cx="12" cy="12" r="10" />
                  <polyline points="12 6 12 12 16 14" />
                </svg>
                <span className="text-gray-600 text-sm">
                  {service.duration || `${service.duration_minutes} mins`}
                </span>
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center mt-auto">
            <div className="flex items-center">
              {service.discounted_price || service.discountedPrice ? (
                <>
                  <span className="font-bold text-lg text-gray-900">
                    ₹{service.discounted_price || service.discountedPrice}
                  </span>
                  <span className="text-gray-400 line-through text-sm ml-2">
                    ₹{service.base_price || service.price}
                  </span>
                </>
              ) : (
                <span className="font-bold text-lg text-gray-900">
                  ₹{service.base_price || service.price}
                </span>
              )}
            </div>

            <div className="flex space-x-2">
              <Link href={`/services/${service.id}`} className="block">
                <div className="px-3 py-1 bg-gray-100 text-gray-700 text-center text-sm font-medium rounded-md hover:bg-gray-200 transition-colors">
                  View
                </div>
              </Link>
              <button
                onClick={handleAddToCart}
                className="bg-primary-500 text-white hover:bg-primary-600 px-3 py-1 rounded-md text-sm font-medium transition-colors"
              >
                Add
              </button>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default VerticalServiceCard;
