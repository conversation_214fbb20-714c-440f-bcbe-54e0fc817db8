'use client';

import React from 'react';
import ServiceCard from './ServiceCard';
import { Service } from '@/lib/types/service';

interface ServiceListProps {
  services: Service[];
}

const ServiceList: React.FC<ServiceListProps> = ({ services }) => {
  return (
    <div className="flex flex-col space-y-4">
      {services.map((service) => (
        <ServiceCard key={service.id} service={service} />
      ))}
    </div>
  );
};

export default ServiceList;
