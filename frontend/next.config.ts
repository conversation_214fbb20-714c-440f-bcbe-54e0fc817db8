import type { NextConfig } from 'next';
import withPWAInit from 'next-pwa';

const withPWA = withPWAInit({
  dest: 'public',
  disable: process.env.NODE_ENV === 'development',
  register: true,
  skipWaiting: true,
});

const nextConfig: NextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
      },
    ],
  },
  async rewrites() {
    return [
      // Customer app routes
      {
        source: '/',
        destination: '/customer',
      },
      // API routes - always add trailing slash to destination
      {
        source: '/api/:path*',
        // Always use port 8000 for backend API
        destination: 'http://localhost:8000/api/v1/:path*/',
      },
    ];
  },
};

export default withPWA(nextConfig);
