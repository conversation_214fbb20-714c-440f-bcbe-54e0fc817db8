const axios = require('axios');

// Function to send OTP
async function sendOTP(phoneNumber) {
  try {
    console.log('Sending OTP to:', phoneNumber);
    const response = await axios.post(
      'http://localhost:8001/api/v1/users/auth/customer/',
      {
        action: 'generate_otp',
        phone_number: phoneNumber,
      }
    );
    console.log('OTP response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error sending OTP:', error.message);
    if (error.response) {
      console.error(
        'Error details:',
        error.response.data,
        error.response.status
      );
    }
    throw error;
  }
}

// Function to verify OTP
async function verifyOTP(phoneNumber, otp) {
  try {
    console.log('Verifying OTP:', phoneNumber, otp);
    const response = await axios.post(
      'http://localhost:8001/api/v1/users/auth/customer/',
      {
        action: 'verify_otp',
        phone_number: phoneNumber,
        otp,
      }
    );
    console.log('Verify OTP response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error verifying OTP:', error.message);
    if (error.response) {
      console.error(
        'Error details:',
        error.response.data,
        error.response.status
      );
    }
    throw error;
  }
}

// Test the API
async function testAPI() {
  const phoneNumber = '9391997662'; // Replace with your phone number

  try {
    // Step 1: Send OTP
    const otpResponse = await sendOTP(phoneNumber);
    console.log('OTP sent successfully. Check your phone for the OTP code.');

    // In a real app, you would wait for user input here
    // For testing, we'll use a hardcoded OTP (you should replace this with the actual OTP you receive)
    const otp = '641246'; // Replace with the OTP you receive

    // Step 2: Verify OTP
    const verifyResponse = await verifyOTP(phoneNumber, otp);
    console.log('OTP verified successfully!');
    console.log('User status:', verifyResponse.user_status);
    console.log('User type:', verifyResponse.user_type);
    console.log(
      'Access token:',
      verifyResponse.tokens.access.substring(0, 20) + '...'
    );
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

// Run the test
testAPI();
