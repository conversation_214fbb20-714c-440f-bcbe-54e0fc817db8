"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import streamlit.proto.WidthConfig_pb2
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class Exception(google.protobuf.message.Message):
    """A python exception.

    NOTE: This proto type is used by some external services so needs to remain
    relatively stable. While it isn't entirely set in stone, changing it
    may require a good amount of effort so should be avoided if possible.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TYPE_FIELD_NUMBER: builtins.int
    MESSAGE_FIELD_NUMBER: builtins.int
    MESSAGE_IS_MARKDOWN_FIELD_NUMBER: builtins.int
    STACK_TRACE_FIELD_NUMBER: builtins.int
    IS_WARNING_FIELD_NUMBER: builtins.int
    WIDTH_CONFIG_FIELD_NUMBER: builtins.int
    type: builtins.str
    """The type of the exception. This can be any string, but is usually a valid
    Python exception type, like 'RuntimeError'.
    """
    message: builtins.str
    """The exception's message."""
    message_is_markdown: builtins.bool
    """If true, the exception message should be rendered as Markdown text."""
    is_warning: builtins.bool
    """If true, this is an error that doesn't stop the execution flow. So it gets
    rendered differently for clarity.
    """
    @property
    def stack_trace(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """The stack trace to print."""

    @property
    def width_config(self) -> streamlit.proto.WidthConfig_pb2.WidthConfig:
        """Indicates the width setting: "stetch", "content" or a pixel value."""

    def __init__(
        self,
        *,
        type: builtins.str = ...,
        message: builtins.str = ...,
        message_is_markdown: builtins.bool = ...,
        stack_trace: collections.abc.Iterable[builtins.str] | None = ...,
        is_warning: builtins.bool = ...,
        width_config: streamlit.proto.WidthConfig_pb2.WidthConfig | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["width_config", b"width_config"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["is_warning", b"is_warning", "message", b"message", "message_is_markdown", b"message_is_markdown", "stack_trace", b"stack_trace", "type", b"type", "width_config", b"width_config"]) -> None: ...

global___Exception = Exception
