# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/Selectbox.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import LabelVisibilityMessage_pb2 as streamlit_dot_proto_dot_LabelVisibilityMessage__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1fstreamlit/proto/Selectbox.proto\x1a,streamlit/proto/LabelVisibilityMessage.proto\"\xe5\x02\n\tSelectbox\x12\n\n\x02id\x18\x01 \x01(\t\x12\r\n\x05label\x18\x02 \x01(\t\x12\x14\n\x07\x64\x65\x66\x61ult\x18\x03 \x01(\x05H\x00\x88\x01\x01\x12\x0f\n\x07options\x18\x04 \x03(\t\x12\x0c\n\x04help\x18\x05 \x01(\t\x12\x0f\n\x07\x66orm_id\x18\x06 \x01(\t\x12\x16\n\x05value\x18\x07 \x01(\x05\x42\x02\x18\x01H\x01\x88\x01\x01\x12\x16\n\traw_value\x18\r \x01(\tH\x02\x88\x01\x01\x12\x11\n\tset_value\x18\x08 \x01(\x08\x12\x10\n\x08\x64isabled\x18\t \x01(\x08\x12\x31\n\x10label_visibility\x18\n \x01(\x0b\x32\x17.LabelVisibilityMessage\x12\x13\n\x0bplaceholder\x18\x0b \x01(\t\x12\x1f\n\x12\x61\x63\x63\x65pt_new_options\x18\x0c \x01(\x08H\x03\x88\x01\x01\x42\n\n\x08_defaultB\x08\n\x06_valueB\x0c\n\n_raw_valueB\x15\n\x13_accept_new_optionsB.\n\x1c\x63om.snowflake.apps.streamlitB\x0eSelectboxProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.Selectbox_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\016SelectboxProto'
  _globals['_SELECTBOX'].fields_by_name['value']._loaded_options = None
  _globals['_SELECTBOX'].fields_by_name['value']._serialized_options = b'\030\001'
  _globals['_SELECTBOX']._serialized_start=82
  _globals['_SELECTBOX']._serialized_end=439
# @@protoc_insertion_point(module_scope)
