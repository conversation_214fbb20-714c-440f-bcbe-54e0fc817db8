import{r as i,j as c,bs as g,bQ as b}from"./index.C1z8KpLA.js";import{a as m}from"./useBasicWidgetState.zXY9CjFS.js";import"./FormClearHelper.B67tgll0.js";const f=(t,e)=>t.getStringValue(e),p=t=>t.default??null,C=t=>t.value??null,d=(t,e,r,o)=>{e.setStringValue(t,r.value,{fromUi:r.fromUi},o)},S=({element:t,disabled:e,widgetMgr:r,fragmentId:o})=>{var l;const[s,a]=m({getStateFromWidgetMgr:f,getDefaultStateFromProto:p,getCurrStateFromProto:C,updateWidgetMgrState:d,element:t,widgetMgr:r,fragmentId:o}),u=i.useCallback(n=>{a({value:n,fromUi:!0})},[a]);return c(b,{label:t.label,labelVisibility:g((l=t.labelVisibility)==null?void 0:l.value),help:t.help,onChange:u,disabled:e,value:s})},k=i.memo(S);export{k as default};
