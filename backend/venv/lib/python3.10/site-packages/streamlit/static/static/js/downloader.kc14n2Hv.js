import{c as b,e as P}from"./es6.CxQz807-.js";import"./index.C1z8KpLA.js";const{WritableStream:y,TransformStream:_,DOMException:v,Blob:u}=b,{GONE:E}=P,M=/constructor/i.test(window.HTMLElement);class F{constructor(e="unkown"){this.name=e,this.kind="file"}async getFile(){throw new v(...E)}async isSameEntry(e){return this===e}async createWritable(e={}){var d;const t=await((d=navigator.serviceWorker)==null?void 0:d.getRegistration()),o=document.createElement("a"),n=new _,R=n.writable;if(o.download=this.name,M||!t){let r=[];n.readable.pipeTo(new y({write(s){r.push(new u([s]))},close(){const s=new u(r,{type:"application/octet-stream; charset=utf-8"});r=[],o.href=URL.createObjectURL(s),o.click(),setTimeout(()=>URL.revokeObjectURL(o.href),1e4)}}))}else{const{writable:r,readablePort:s}=new j(y),a=encodeURIComponent(this.name).replace(/['()]/g,escape).replace(/\*/g,"%2A"),f={"content-disposition":"attachment; filename*=UTF-8''"+a,"content-type":"application/octet-stream; charset=utf-8",...e.size?{"content-length":e.size}:{}},g=setTimeout(()=>t.active.postMessage(0),1e4);n.readable.pipeThrough(new _({transform(c,h){if(c instanceof Uint8Array)return h.enqueue(c);const w=new Response(c).body.getReader(),p=W=>w.read().then(m=>m.done?0:p(h.enqueue(m.value)));return p()}})).pipeTo(r).finally(()=>{clearInterval(g)}),t.active.postMessage({url:t.scope+a,headers:f,readablePort:s},[s]);const i=document.createElement("iframe");i.hidden=!0,i.src=t.scope+a,document.body.appendChild(i)}return R.getWriter()}}const T=0,L=0,O=1,U=1,k=2;class S{constructor(e){e.onmessage=t=>this._onMessage(t.data),this._port=e,this._resetReady()}start(e){return this._controller=e,this._readyPromise}write(e){const t={type:T,chunk:e};return this._port.postMessage(t,[e.buffer]),this._resetReady(),this._readyPromise}close(){this._port.postMessage({type:k}),this._port.close()}abort(e){this._port.postMessage({type:U,reason:e}),this._port.close()}_onMessage(e){e.type===L&&this._resolveReady(),e.type===O&&this._onError(e.reason)}_onError(e){this._controller.error(e),this._rejectReady(e),this._port.close()}_resetReady(){this._readyPromise=new Promise((e,t)=>{this._readyResolve=e,this._readyReject=t}),this._readyPending=!0}_resolveReady(){this._readyResolve(),this._readyPending=!1}_rejectReady(e){this._readyPending||this._resetReady(),this._readyPromise.catch(()=>{}),this._readyReject(e),this._readyPending=!1}}class j{constructor(e){const t=new MessageChannel;this.readablePort=t.port1,this.writable=new e(new S(t.port2))}}export{F as FileHandle};
