Metadata-Version: 2.4
Name: jsonschema-specifications
Version: 2025.4.1
Summary: The JSON Schema meta-schemas and vocabularies, exposed as a Registry
Project-URL: Documentation, https://jsonschema-specifications.readthedocs.io/
Project-URL: Homepage, https://github.com/python-jsonschema/jsonschema-specifications
Project-URL: Issues, https://github.com/python-jsonschema/jsonschema-specifications/issues/
Project-URL: Funding, https://github.com/sponsors/Julian
Project-URL: Tidelift, https://tidelift.com/subscription/pkg/pypi-jsonschema-specifications?utm_source=pypi-jsonschema-specifications&utm_medium=referral&utm_campaign=pypi-link
Project-URL: Source, https://github.com/python-jsonschema/jsonschema-specifications
Author-email: <PERSON> <<EMAIL>>
License-Expression: MIT
License-File: COPYING
Keywords: data validation,json,json schema,jsonschema,validation
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: File Formats :: JSON
Classifier: Topic :: File Formats :: JSON :: JSON Schema
Requires-Python: >=3.9
Requires-Dist: referencing>=0.31.0
Description-Content-Type: text/x-rst

=============================
``jsonschema-specifications``
=============================

|PyPI| |Pythons| |CI| |ReadTheDocs|

JSON support files from the `JSON Schema Specifications <https://json-schema.org/specification.html>`_ (metaschemas, vocabularies, etc.), packaged for runtime access from Python as a `referencing-based Schema Registry <https://referencing.readthedocs.io/en/stable/api/#referencing.Registry>`_.

.. |PyPI| image:: https://img.shields.io/pypi/v/jsonschema-specifications.svg
  :alt: PyPI version
  :target: https://pypi.org/project/jsonschema-specifications/

.. |Pythons| image:: https://img.shields.io/pypi/pyversions/jsonschema-specifications.svg
  :alt: Supported Python versions
  :target: https://pypi.org/project/jsonschema-specifications/

.. |CI| image:: https://github.com/python-jsonschema/jsonschema-specifications/workflows/CI/badge.svg
  :alt: Build status
  :target: https://github.com/python-jsonschema/jsonschema-specifications/actions?query=workflow%3ACI

.. |ReadTheDocs| image:: https://readthedocs.org/projects/jsonschema-specifications/badge/?version=stable&style=flat
  :alt: ReadTheDocs status
  :target: https://jsonschema-specifications.readthedocs.io/en/stable/
