from rest_framework import serializers
from .models import Cart, Cart<PERSON><PERSON>, CartItemAddon
from apps.services.serializers import ServiceSerializer, ServiceAddonSerializer

class CartItemAddonSerializer(serializers.ModelSerializer):
    addon = ServiceAddonSerializer(read_only=True)
    addon_id = serializers.UUIDField(write_only=True)
    
    class Meta:
        model = CartItemAddon
        fields = ['id', 'addon', 'addon_id', 'quantity', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

class CartItemSerializer(serializers.ModelSerializer):
    service = ServiceSerializer(read_only=True)
    service_id = serializers.UUIDField(write_only=True)
    addons = CartItemAddonSerializer(many=True, read_only=True)
    
    class Meta:
        model = CartItem
        fields = ['id', 'service', 'service_id', 'quantity', 'addons', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        cart = validated_data.pop('cart')
        service_id = validated_data.pop('service_id')
        
        # Get or create cart item
        cart_item, created = CartItem.objects.get_or_create(
            cart=cart,
            service_id=service_id,
            defaults=validated_data
        )
        
        # If cart item already exists, update quantity
        if not created:
            cart_item.quantity = validated_data.get('quantity', cart_item.quantity)
            cart_item.save()
        
        return cart_item

class CartSerializer(serializers.ModelSerializer):
    items = CartItemSerializer(many=True, read_only=True)
    
    class Meta:
        model = Cart
        fields = ['id', 'customer', 'session_id', 'items', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def to_representation(self, instance):
        data = super().to_representation(instance)
        
        # Calculate totals
        subtotal = 0
        discount = 0
        
        for item in instance.items.all():
            service = item.service
            price = service.base_price
            discounted_price = getattr(service, 'discounted_price', None)
            
            if discounted_price:
                subtotal += discounted_price * item.quantity
                discount += (price - discounted_price) * item.quantity
            else:
                subtotal += price * item.quantity
            
            # Add addon prices
            for addon_item in item.addons.all():
                subtotal += addon_item.addon.price * addon_item.quantity
        
        data['subtotal'] = subtotal
        data['discount'] = discount
        data['total'] = subtotal
        
        return data
