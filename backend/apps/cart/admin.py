from django.contrib import admin
from .models import Cart, CartItem, CartItemAddon

class CartItemAddonInline(admin.TabularInline):
    model = CartItemAddon
    extra = 0

class CartItemInline(admin.TabularInline):
    model = CartItem
    extra = 0
    inlines = [CartItemAddonInline]

@admin.register(Cart)
class CartAdmin(admin.ModelAdmin):
    list_display = ('id', 'customer', 'session_id', 'created_at', 'updated_at')
    list_filter = ('created_at', 'updated_at')
    search_fields = ('customer__first_name', 'customer__last_name', 'session_id')
    inlines = [CartItemInline]

@admin.register(CartItem)
class CartItemAdmin(admin.ModelAdmin):
    list_display = ('id', 'cart', 'service', 'quantity', 'created_at', 'updated_at')
    list_filter = ('created_at', 'updated_at')
    search_fields = ('cart__customer__first_name', 'cart__customer__last_name', 'service__name')
    inlines = [CartItemAddonInline]

@admin.register(CartItemAddon)
class CartItemAddonAdmin(admin.ModelAdmin):
    list_display = ('id', 'cart_item', 'addon', 'quantity', 'created_at', 'updated_at')
    list_filter = ('created_at', 'updated_at')
    search_fields = ('cart_item__service__name', 'addon__name')
