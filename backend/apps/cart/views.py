from datetime import datetime, timed<PERSON>ta
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db import transaction
from .models import Cart, CartItem, CartItemAddon
from .serializers import CartSerializer, CartItemSerializer, CartItemAddonSerializer
from apps.services.models import Service, ServiceAddon
from apps.customers.models import CustomerAddress
from apps.bookings.models import Booking, BookingAddon
from apps.bookings.serializers import BookingSerializer

class CartViewSet(viewsets.ModelViewSet):
    serializer_class = CartSerializer

    def get_queryset(self):
        # Get cart for authenticated user or session
        if self.request.user.is_authenticated:
            return Cart.objects.filter(customer__user=self.request.user)

        session_id = self.request.session.session_key
        if not session_id:
            self.request.session.create()
            session_id = self.request.session.session_key

        return Cart.objects.filter(session_id=session_id)

    def get_object(self):
        # Get or create cart for authenticated user or session
        if self.request.user.is_authenticated:
            try:
                customer = self.request.user.customer
                cart, created = Cart.objects.get_or_create(
                    customer=customer
                )
            except:
                # If user doesn't have a customer profile, use session
                session_id = self.request.session.session_key
                if not session_id:
                    self.request.session.create()
                    session_id = self.request.session.session_key

                cart, created = Cart.objects.get_or_create(
                    session_id=session_id
                )
        else:
            session_id = self.request.session.session_key
            if not session_id:
                self.request.session.create()
                session_id = self.request.session.session_key

            cart, created = Cart.objects.get_or_create(
                session_id=session_id
            )

        return cart

    @action(detail=False, methods=['get'])
    def my_cart(self, request):
        cart = self.get_object()
        serializer = self.get_serializer(cart)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def add_item(self, request):
        cart = self.get_object()

        # Validate service_id
        service_id = request.data.get('service_id')
        try:
            service = Service.objects.get(id=service_id)
        except Service.DoesNotExist:
            return Response(
                {'error': 'Service not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Create or update cart item
        quantity = int(request.data.get('quantity', 1))

        serializer = CartItemSerializer(data={
            'service_id': service_id,
            'quantity': quantity
        })
        serializer.is_valid(raise_exception=True)
        serializer.save(cart=cart)

        # Return updated cart
        cart_serializer = self.get_serializer(cart)
        return Response(cart_serializer.data)

    @action(detail=False, methods=['post'])
    def add_addon(self, request):
        cart = self.get_object()

        # Validate cart item
        cart_item_id = request.data.get('cart_item_id')
        try:
            cart_item = CartItem.objects.get(id=cart_item_id, cart=cart)
        except CartItem.DoesNotExist:
            return Response(
                {'error': 'Cart item not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Validate addon
        addon_id = request.data.get('addon_id')
        try:
            addon = ServiceAddon.objects.get(id=addon_id, service=cart_item.service)
        except ServiceAddon.DoesNotExist:
            return Response(
                {'error': 'Addon not found or not available for this service'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Create or update cart item addon
        quantity = int(request.data.get('quantity', 1))

        cart_item_addon, created = CartItemAddon.objects.get_or_create(
            cart_item=cart_item,
            addon=addon,
            defaults={'quantity': quantity}
        )

        if not created:
            cart_item_addon.quantity = quantity
            cart_item_addon.save()

        # Return updated cart
        cart_serializer = self.get_serializer(cart)
        return Response(cart_serializer.data)

    @action(detail=False, methods=['post'])
    def update_item(self, request):
        cart = self.get_object()

        # Validate cart item
        item_id = request.data.get('item_id')
        try:
            cart_item = CartItem.objects.get(id=item_id, cart=cart)
        except CartItem.DoesNotExist:
            return Response(
                {'error': 'Cart item not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Update quantity
        quantity = int(request.data.get('quantity', 1))
        if quantity <= 0:
            cart_item.delete()
        else:
            cart_item.quantity = quantity
            cart_item.save()

        # Return updated cart
        cart_serializer = self.get_serializer(cart)
        return Response(cart_serializer.data)

    @action(detail=False, methods=['post'])
    def remove_item(self, request):
        cart = self.get_object()

        # Validate cart item
        item_id = request.data.get('item_id')
        try:
            cart_item = CartItem.objects.get(id=item_id, cart=cart)
        except CartItem.DoesNotExist:
            return Response(
                {'error': 'Cart item not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Remove item
        cart_item.delete()

        # Return updated cart
        cart_serializer = self.get_serializer(cart)
        return Response(cart_serializer.data)

    @action(detail=False, methods=['post'])
    def remove_addon(self, request):
        cart = self.get_object()

        # Validate cart item addon
        addon_id = request.data.get('addon_id')
        cart_item_id = request.data.get('cart_item_id')

        try:
            cart_item = CartItem.objects.get(id=cart_item_id, cart=cart)
            cart_item_addon = CartItemAddon.objects.get(id=addon_id, cart_item=cart_item)
        except (CartItem.DoesNotExist, CartItemAddon.DoesNotExist):
            return Response(
                {'error': 'Cart item addon not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Remove addon
        cart_item_addon.delete()

        # Return updated cart
        cart_serializer = self.get_serializer(cart)
        return Response(cart_serializer.data)

    @action(detail=False, methods=['post'])
    def clear(self, request):
        cart = self.get_object()

        # Remove all items
        cart.items.all().delete()

        # Return updated cart
        cart_serializer = self.get_serializer(cart)
        return Response(cart_serializer.data)

    @action(detail=False, methods=['post'])
    @transaction.atomic
    def checkout(self, request):
        cart = self.get_object()

        if not cart.items.exists():
            return Response(
                {'error': 'Cart is empty'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate required checkout data
        booking_date = request.data.get('booking_date')
        start_time = request.data.get('start_time')
        address_id = request.data.get('address_id')

        if not all([booking_date, start_time, address_id]):
            return Response(
                {'error': 'Missing required booking information'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Get customer and address
            if not request.user.is_authenticated:
                return Response(
                    {'error': 'You must be logged in to checkout'},
                    status=status.HTTP_401_UNAUTHORIZED
                )

            customer = request.user.customer
            address = CustomerAddress.objects.get(id=address_id, customer=customer)

            # Create bookings for each cart item
            bookings = []
            for item in cart.items.all():
                service = item.service

                # Calculate service duration
                duration_minutes = service.duration_minutes
                end_time_obj = datetime.strptime(start_time, '%H:%M:%S')
                end_time_obj += timedelta(minutes=duration_minutes)
                end_time = end_time_obj.strftime('%H:%M:%S')

                # Calculate amounts
                base_price = service.base_price
                discount_amount = 0
                if hasattr(service, 'discount_percentage') and service.discount_percentage > 0:
                    discount_amount = (base_price * service.discount_percentage) / 100

                # Create booking
                booking = Booking.objects.create(
                    customer=customer,
                    service=service,
                    address=address,
                    booking_date=booking_date,
                    start_time=start_time,
                    end_time=end_time,
                    total_amount=base_price * item.quantity,
                    discount_amount=discount_amount * item.quantity,
                    final_amount=(base_price - discount_amount) * item.quantity
                )

                # Add addons to booking
                for addon_item in item.addons.all():
                    BookingAddon.objects.create(
                        booking=booking,
                        addon=addon_item.addon,
                        price=addon_item.addon.price
                    )

                bookings.append(booking)

            # Clear the cart after successful checkout
            cart.items.all().delete()

            # Return booking information
            booking_serializer = BookingSerializer(bookings, many=True)
            return Response(booking_serializer.data, status=status.HTTP_201_CREATED)

        except CustomerAddress.DoesNotExist:
            return Response(
                {'error': 'Address not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
