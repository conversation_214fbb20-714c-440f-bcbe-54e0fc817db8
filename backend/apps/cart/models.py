import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from apps.customers.models import Customer
from apps.services.models import Service, ServiceAddon

class Cart(models.Model):
    """
    Shopping cart for customers before creating bookings.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    customer = models.OneToOneField(Customer, on_delete=models.CASCADE, related_name='cart', null=True, blank=True)
    session_id = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        constraints = [
            models.CheckConstraint(
                check=models.Q(customer__isnull=False) | models.Q(session_id__isnull=False),
                name='cart_customer_or_session'
            )
        ]
    
    def __str__(self):
        if self.customer:
            return f"Cart for {self.customer.first_name}"
        return f"Cart for session {self.session_id}"

class CartItem(models.Model):
    """
    Items in a shopping cart.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    cart = models.ForeignKey(Cart, on_delete=models.CASCADE, related_name='items')
    service = models.ForeignKey(Service, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('cart', 'service')
    
    def __str__(self):
        return f"{self.quantity} x {self.service.name} in cart {self.cart.id}"

class CartItemAddon(models.Model):
    """
    Add-ons for cart items.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    cart_item = models.ForeignKey(CartItem, on_delete=models.CASCADE, related_name='addons')
    addon = models.ForeignKey(ServiceAddon, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        unique_together = ('cart_item', 'addon')
    
    def __str__(self):
        return f"{self.quantity} x {self.addon.name} for {self.cart_item}"
